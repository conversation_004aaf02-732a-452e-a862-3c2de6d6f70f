-- Create tables for the $1 Party Booking offer system

-- User booking count tracking
CREATE TABLE IF NOT EXISTS user_booking_counts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL UNIQUE,
    total_bookings INTEGER DEFAULT 0,
    completed_bookings INTEGER DEFAULT 0,
    cancelled_bookings INTEGER DEFAULT 0,
    last_booking_date TIMESTAMPTZ,
    next_discount_at INTEGER DEFAULT 5, -- Next booking number that gets discount
    discount_bookings_used INTEGER DEFAULT 0, -- How many discount bookings used
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Booking offer applications
CREATE TABLE IF NOT EXISTS booking_offer_applications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    booking_id UUID NOT NULL, -- Reference to actual booking
    user_id TEXT NOT NULL,
    offer_type TEXT NOT NULL CHECK (offer_type IN ('first_booking', 'fifth_booking')),
    original_fee_amount DECIMAL(10,2) NOT NULL, -- What the fee would have been (5% of venue cost)
    discounted_fee_amount DECIMAL(10,2) NOT NULL DEFAULT 1.00, -- Always $1
    venue_cost DECIMAL(10,2) NOT NULL,
    savings_amount DECIMAL(10,2) NOT NULL, -- original_fee_amount - 1.00
    booking_number INTEGER NOT NULL, -- Which booking number this was for the user
    applied_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one offer per booking
    UNIQUE(booking_id)
);

-- Offer eligibility tracking
CREATE TABLE IF NOT EXISTS offer_eligibility_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    booking_number INTEGER NOT NULL,
    is_eligible BOOLEAN NOT NULL,
    offer_type TEXT CHECK (offer_type IN ('first_booking', 'fifth_booking')),
    reason TEXT, -- Why eligible/not eligible
    checked_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE user_booking_counts ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_offer_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE offer_eligibility_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_booking_counts
CREATE POLICY "Users can view own booking counts" ON user_booking_counts
    FOR SELECT USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own booking counts" ON user_booking_counts
    FOR INSERT WITH CHECK (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can upsert own booking counts" ON user_booking_counts
    FOR UPDATE USING (user_id = auth.jwt() ->> 'sub')
    WITH CHECK (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Admins can manage all booking counts" ON user_booking_counts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE clerk_id = auth.jwt() ->> 'sub' 
            AND role = 'admin'
        )
    );

-- RLS Policies for booking_offer_applications
CREATE POLICY "Users can view own offer applications" ON booking_offer_applications
    FOR SELECT USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "System can insert offer applications" ON booking_offer_applications
    FOR INSERT WITH CHECK (true); -- Allow system to create offers

CREATE POLICY "Admins can manage all offer applications" ON booking_offer_applications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE clerk_id = auth.jwt() ->> 'sub' 
            AND role = 'admin'
        )
    );

-- RLS Policies for offer_eligibility_log
CREATE POLICY "Users can view own eligibility log" ON offer_eligibility_log
    FOR SELECT USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "System can insert eligibility log" ON offer_eligibility_log
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all eligibility logs" ON offer_eligibility_log
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE clerk_id = auth.jwt() ->> 'sub' 
            AND role = 'admin'
        )
    );

-- Functions for booking offer management

-- Function to get user's current booking count and eligibility
CREATE OR REPLACE FUNCTION get_user_booking_status(p_user_id TEXT)
RETURNS TABLE (
    total_bookings INTEGER,
    completed_bookings INTEGER,
    next_discount_at INTEGER,
    discount_bookings_used INTEGER,
    is_eligible_for_discount BOOLEAN,
    offer_type TEXT,
    bookings_until_next_discount INTEGER
) AS $$
DECLARE
    v_counts user_booking_counts%ROWTYPE;
    v_is_eligible BOOLEAN := FALSE;
    v_offer_type TEXT := NULL;
    v_until_next INTEGER := 0;
BEGIN
    -- Get or create user booking counts
    SELECT * INTO v_counts
    FROM user_booking_counts
    WHERE user_id = p_user_id;
    
    IF NOT FOUND THEN
        INSERT INTO user_booking_counts (user_id, total_bookings, completed_bookings, next_discount_at, discount_bookings_used)
        VALUES (p_user_id, 0, 0, 5, 0)
        RETURNING * INTO v_counts;
    END IF;
    
    -- Check eligibility
    IF v_counts.total_bookings = 0 THEN
        -- First booking gets discount
        v_is_eligible := TRUE;
        v_offer_type := 'first_booking';
        v_until_next := 0;
    ELSIF (v_counts.completed_bookings + 1) = v_counts.next_discount_at THEN
        -- Every 5th completed booking gets discount
        v_is_eligible := TRUE;
        v_offer_type := 'fifth_booking';
        v_until_next := 0;
    ELSE
        -- Calculate how many bookings until next discount
        v_until_next := v_counts.next_discount_at - (v_counts.completed_bookings + 1);
        IF v_until_next < 0 THEN
            v_until_next := 5 - ((v_counts.completed_bookings + 1 - v_counts.next_discount_at) % 5);
        END IF;
    END IF;
    
    RETURN QUERY SELECT 
        v_counts.total_bookings,
        v_counts.completed_bookings,
        v_counts.next_discount_at,
        v_counts.discount_bookings_used,
        v_is_eligible,
        v_offer_type,
        v_until_next;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to apply booking offer
CREATE OR REPLACE FUNCTION apply_booking_offer(
    p_booking_id UUID,
    p_user_id TEXT,
    p_venue_cost DECIMAL(10,2)
) RETURNS TABLE (
    applied BOOLEAN,
    offer_type TEXT,
    original_fee DECIMAL(10,2),
    discounted_fee DECIMAL(10,2),
    savings DECIMAL(10,2),
    booking_number INTEGER
) AS $$
DECLARE
    v_status RECORD;
    v_original_fee DECIMAL(10,2);
    v_savings DECIMAL(10,2);
    v_booking_number INTEGER;
BEGIN
    -- Get current booking status
    SELECT * INTO v_status FROM get_user_booking_status(p_user_id);
    
    -- Calculate original fee (5% of venue cost)
    v_original_fee := p_venue_cost * 0.05;
    v_savings := v_original_fee - 1.00;
    v_booking_number := v_status.total_bookings + 1;
    
    IF v_status.is_eligible_for_discount THEN
        -- Apply the offer
        INSERT INTO booking_offer_applications (
            booking_id,
            user_id,
            offer_type,
            original_fee_amount,
            discounted_fee_amount,
            venue_cost,
            savings_amount,
            booking_number
        ) VALUES (
            p_booking_id,
            p_user_id,
            v_status.offer_type,
            v_original_fee,
            1.00,
            p_venue_cost,
            v_savings,
            v_booking_number
        );
        
        -- Update booking counts
        INSERT INTO user_booking_counts (user_id, total_bookings, completed_bookings, next_discount_at, discount_bookings_used)
        VALUES (p_user_id, v_booking_number, 0, 
                CASE WHEN v_status.offer_type = 'first_booking' THEN 5 
                     ELSE v_status.next_discount_at + 5 END,
                v_status.discount_bookings_used + 1)
        ON CONFLICT (user_id) DO UPDATE SET
            total_bookings = v_booking_number,
            next_discount_at = CASE WHEN v_status.offer_type = 'first_booking' THEN 5 
                                   ELSE user_booking_counts.next_discount_at + 5 END,
            discount_bookings_used = user_booking_counts.discount_bookings_used + 1,
            updated_at = NOW();
        
        -- Log eligibility check
        INSERT INTO offer_eligibility_log (user_id, booking_number, is_eligible, offer_type, reason)
        VALUES (p_user_id, v_booking_number, TRUE, v_status.offer_type, 'Offer applied successfully');
        
        RETURN QUERY SELECT TRUE, v_status.offer_type, v_original_fee, 1.00::DECIMAL(10,2), v_savings, v_booking_number;
    ELSE
        -- Not eligible, just update booking count
        INSERT INTO user_booking_counts (user_id, total_bookings, completed_bookings, next_discount_at, discount_bookings_used)
        VALUES (p_user_id, v_booking_number, 0, v_status.next_discount_at, v_status.discount_bookings_used)
        ON CONFLICT (user_id) DO UPDATE SET
            total_bookings = v_booking_number,
            updated_at = NOW();
        
        -- Log eligibility check
        INSERT INTO offer_eligibility_log (user_id, booking_number, is_eligible, offer_type, reason)
        VALUES (p_user_id, v_booking_number, FALSE, NULL, 'Not eligible for discount');
        
        RETURN QUERY SELECT FALSE, NULL::TEXT, v_original_fee, v_original_fee, 0.00::DECIMAL(10,2), v_booking_number;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark booking as completed (for tracking completed bookings)
CREATE OR REPLACE FUNCTION mark_booking_completed(
    p_booking_id UUID,
    p_user_id TEXT
) RETURNS VOID AS $$
BEGIN
    -- Update completed bookings count
    UPDATE user_booking_counts 
    SET completed_bookings = completed_bookings + 1,
        last_booking_date = NOW(),
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- If no record exists, create one
    IF NOT FOUND THEN
        INSERT INTO user_booking_counts (user_id, total_bookings, completed_bookings, last_booking_date)
        VALUES (p_user_id, 1, 1, NOW());
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's offer history
CREATE OR REPLACE FUNCTION get_user_offer_history(p_user_id TEXT)
RETURNS TABLE (
    booking_id UUID,
    offer_type TEXT,
    original_fee_amount DECIMAL(10,2),
    savings_amount DECIMAL(10,2),
    venue_cost DECIMAL(10,2),
    booking_number INTEGER,
    applied_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        boa.booking_id,
        boa.offer_type,
        boa.original_fee_amount,
        boa.savings_amount,
        boa.venue_cost,
        boa.booking_number,
        boa.applied_at
    FROM booking_offer_applications boa
    WHERE boa.user_id = p_user_id
    ORDER BY boa.applied_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_booking_counts_user_id ON user_booking_counts(user_id);
CREATE INDEX IF NOT EXISTS idx_booking_offer_applications_user_id ON booking_offer_applications(user_id);
CREATE INDEX IF NOT EXISTS idx_booking_offer_applications_booking_id ON booking_offer_applications(booking_id);
CREATE INDEX IF NOT EXISTS idx_offer_eligibility_log_user_id ON offer_eligibility_log(user_id);

-- Insert initial data for existing users (optional)
-- This would set existing users to have 0 bookings
INSERT INTO user_booking_counts (user_id, total_bookings, completed_bookings, next_discount_at, discount_bookings_used)
SELECT DISTINCT clerk_id, 0, 0, 5, 0
FROM user_profiles
WHERE clerk_id IS NOT NULL
ON CONFLICT (user_id) DO NOTHING;
