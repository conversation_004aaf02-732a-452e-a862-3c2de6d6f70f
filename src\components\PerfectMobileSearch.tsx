import React, { useState, useEffect, useRef } from 'react';
import { Search, MapPin, Clock, Users } from 'lucide-react';

interface PerfectMobileSearchProps {
  onSearch: (filters: any) => void;
}

export const PerfectMobileSearch: React.FC<PerfectMobileSearchProps> = ({ onSearch }) => {
  const [location, setLocation] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const popularLocations = ['Sydney CBD', 'Bondi', 'Newtown', 'Manly', 'Surry Hills'];
  
  const handleSearch = () => {
    if (location.trim()) {
      onSearch({ location: location.trim() });
      setShowSuggestions(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const selectLocation = (loc: string) => {
    setLocation(loc);
    onSearch({ location: loc });
    setShowSuggestions(false);
  };

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(e.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="relative px-3 py-2 bg-white border-b border-gray-100">
      <div className="relative">
        <MapPin size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
        <input
          ref={inputRef}
          type="text"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onKeyDown={handleKeyDown}
          placeholder="Where's your party?"
          className="w-full pl-10 pr-10 py-3 bg-gray-50 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:bg-white"
        />
        <button
          onClick={handleSearch}
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-orange-500 text-white p-2 rounded-md active:scale-95"
          aria-label="Search venues"
        >
          <Search size={16} />
        </button>
      </div>

      {/* Suggestions */}
      {isFocused && location.length === 0 && (
        <div className="absolute top-full left-3 right-3 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="p-2">
            <p className="text-xs text-gray-500 mb-2">Popular locations</p>
            {popularLocations.map((loc) => (
              <button
                key={loc}
                onClick={() => selectLocation(loc)}
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded flex items-center"
              >
                <MapPin size={14} className="mr-2 text-gray-400" />
                {loc}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
