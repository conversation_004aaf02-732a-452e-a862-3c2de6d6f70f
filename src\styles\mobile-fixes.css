/* Mobile-First Responsive Fixes - Desktop Scale */

/* Prevent horizontal scroll */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Mobile typography - smaller like desktop */
@media (max-width: 768px) {
  html {
    font-size: 14px; /* Smaller like desktop */
  }
  
  body {
    font-size: 0.875rem;
    line-height: 1.4;
  }
  
  h1 { font-size: 1.25rem; }
  h2 { font-size: 1.125rem; }
  h3 { font-size: 1rem; }
  
  /* Compact spacing */
  .container-width {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  
  /* Compact buttons */
  .btn, .button {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  /* Compact cards */
  .venue-card {
    margin: 0.25rem;
    padding: 0.75rem;
  }
  
  /* Compact forms */
  input, select, textarea {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
  }
}

/* Horizontal layout for mobile search */
@media (max-width: 768px) {
  .mobile-search-horizontal {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .mobile-search-horizontal > * {
    flex: 1;
    min-width: 0;
  }
}

/* Compact navigation */
@media (max-width: 768px) {
  .mobile-nav {
    padding: 0.25rem 0;
  }
  
  .mobile-nav-item {
    padding: 0.375rem;
    font-size: 0.75rem;
  }
}

/* Safe area for modern phones */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0.5rem);
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
