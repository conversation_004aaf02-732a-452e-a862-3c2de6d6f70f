import React, { useState } from 'react';
import { Search, MapPin } from 'lucide-react';

interface MicroSearchProps {
  onSearch: (filters: any) => void;
}

export const MicroSearch: React.FC<MicroSearchProps> = ({ onSearch }) => {
  const [location, setLocation] = useState('');

  const handleSearch = () => {
    onSearch({ location });
  };

  return (
    <div className="bg-white border-b border-gray-100 px-2 py-1">
      <div className="flex items-center gap-1">
        <div className="flex-1 relative">
          <MapPin className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
          <input
            type="text"
            placeholder="Where?"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full pl-6 pr-2 py-1 text-xs border-0 focus:outline-none"
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
        </div>
        <button
          onClick={handleSearch}
          className="p-1 bg-orange-500 text-white rounded"
        >
          <Search size={12} />
        </button>
      </div>
    </div>
  );
};

export default MicroSearch;
