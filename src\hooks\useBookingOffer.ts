/**
 * Hook for managing the $1 Party Booking offer system
 */

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { supabase } from '../lib/supabase';

interface BookingStatus {
  totalBookings: number;
  completedBookings: number;
  nextDiscountAt: number;
  discountBookingsUsed: number;
  isEligibleForDiscount: boolean;
  offerType: string | null;
  bookingsUntilNextDiscount: number;
}

interface OfferApplication {
  applied: boolean;
  offerType: string | null;
  originalFee: number;
  discountedFee: number;
  savings: number;
  bookingNumber: number;
}

interface OfferHistory {
  bookingId: string;
  offerType: string;
  originalFeeAmount: number;
  savingsAmount: number;
  venueCost: number;
  bookingNumber: number;
  appliedAt: string;
}

export function useBookingOffer() {
  const { user } = useUser();
  const [bookingStatus, setBookingStatus] = useState<BookingStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchBookingStatus();
    }
  }, [user?.id]);

  const fetchBookingStatus = async (): Promise<BookingStatus | null> => {
    if (!user?.id) return null;

    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .rpc('get_user_booking_status', { p_user_id: user.id });

      if (error) {
        console.error('Error fetching booking status:', error);
        setError('Unable to load offer status');
        return null;
      }

      if (data && data.length > 0) {
        const status = data[0];
        const bookingStatus: BookingStatus = {
          totalBookings: status.total_bookings,
          completedBookings: status.completed_bookings,
          nextDiscountAt: status.next_discount_at,
          discountBookingsUsed: status.discount_bookings_used,
          isEligibleForDiscount: status.is_eligible_for_discount,
          offerType: status.offer_type,
          bookingsUntilNextDiscount: status.bookings_until_next_discount
        };

        setBookingStatus(bookingStatus);
        return bookingStatus;
      }

      return null;
    } catch (error) {
      console.error('Error fetching booking status:', error);
      setError('Unable to load offer status');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const applyBookingOffer = async (
    bookingId: string, 
    venueCost: number
  ): Promise<OfferApplication | null> => {
    if (!user?.id) {
      setError('User not authenticated');
      return null;
    }

    try {
      const { data, error } = await supabase
        .rpc('apply_booking_offer', {
          p_booking_id: bookingId,
          p_user_id: user.id,
          p_venue_cost: venueCost
        });

      if (error) {
        console.error('Error applying booking offer:', error);
        setError('Unable to apply offer');
        return null;
      }

      if (data && data.length > 0) {
        const result = data[0];
        const offerApplication: OfferApplication = {
          applied: result.applied,
          offerType: result.offer_type,
          originalFee: result.original_fee,
          discountedFee: result.discounted_fee,
          savings: result.savings,
          bookingNumber: result.booking_number
        };

        // Refresh booking status after applying offer
        await fetchBookingStatus();

        return offerApplication;
      }

      return null;
    } catch (error) {
      console.error('Error applying booking offer:', error);
      setError('Unable to apply offer');
      return null;
    }
  };

  const markBookingCompleted = async (bookingId: string): Promise<boolean> => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    try {
      const { error } = await supabase
        .rpc('mark_booking_completed', {
          p_booking_id: bookingId,
          p_user_id: user.id
        });

      if (error) {
        console.error('Error marking booking completed:', error);
        setError('Unable to update booking status');
        return false;
      }

      // Refresh booking status after marking completed
      await fetchBookingStatus();
      return true;
    } catch (error) {
      console.error('Error marking booking completed:', error);
      setError('Unable to update booking status');
      return false;
    }
  };

  const getOfferHistory = async (): Promise<OfferHistory[]> => {
    if (!user?.id) return [];

    try {
      const { data, error } = await supabase
        .rpc('get_user_offer_history', { p_user_id: user.id });

      if (error) {
        console.error('Error fetching offer history:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching offer history:', error);
      return [];
    }
  };

  const calculateBookingFee = (venueCost: number): { normalFee: number; discountedFee: number; savings: number } => {
    const normalFee = venueCost * 0.05; // 5% booking fee
    const discountedFee = 1.00; // $1 offer
    const savings = Math.max(0, normalFee - discountedFee);

    return {
      normalFee: Math.round(normalFee * 100) / 100,
      discountedFee,
      savings: Math.round(savings * 100) / 100
    };
  };

  const isEligibleForOffer = (): boolean => {
    return bookingStatus?.isEligibleForDiscount || false;
  };

  const getOfferType = (): string | null => {
    return bookingStatus?.offerType || null;
  };

  const getProgressToNextDiscount = (): { current: number; target: number; remaining: number } => {
    if (!bookingStatus) {
      return { current: 0, target: 5, remaining: 5 };
    }

    const current = bookingStatus.completedBookings % 5;
    const target = 5;
    const remaining = bookingStatus.bookingsUntilNextDiscount;

    return { current, target, remaining };
  };

  const getOfferSummary = (venueCost: number = 0) => {
    const fees = calculateBookingFee(venueCost);
    const progress = getProgressToNextDiscount();

    return {
      isEligible: isEligibleForOffer(),
      offerType: getOfferType(),
      normalFee: fees.normalFee,
      discountedFee: fees.discountedFee,
      savings: fees.savings,
      progress,
      totalBookings: bookingStatus?.totalBookings || 0,
      completedBookings: bookingStatus?.completedBookings || 0,
      discountBookingsUsed: bookingStatus?.discountBookingsUsed || 0
    };
  };

  return {
    bookingStatus,
    loading,
    error,
    fetchBookingStatus,
    applyBookingOffer,
    markBookingCompleted,
    getOfferHistory,
    calculateBookingFee,
    isEligibleForOffer,
    getOfferType,
    getProgressToNextDiscount,
    getOfferSummary
  };
}
