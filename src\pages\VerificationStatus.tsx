/**
 * Verification Status Page
 * Shows the current status of the document verification system
 */

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { Shield, CheckCircle, AlertCircle, Database, Cloud, Settings } from 'lucide-react';
import { supabase } from '../lib/supabase';

interface SystemStatus {
  database: 'connected' | 'error' | 'checking';
  cloudinary: 'configured' | 'missing' | 'checking';
  netlifyFunctions: 'deployed' | 'missing' | 'checking';
  tables: string[];
  errors: string[];
}

export default function VerificationStatus() {
  const { user } = useUser();
  const [status, setStatus] = useState<SystemStatus>({
    database: 'checking',
    cloudinary: 'checking',
    netlifyFunctions: 'checking',
    tables: [],
    errors: []
  });

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    const newStatus: SystemStatus = {
      database: 'checking',
      cloudinary: 'checking',
      netlifyFunctions: 'checking',
      tables: [],
      errors: []
    };

    // Check database connection and tables
    try {
      const { data: tables, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .like('table_name', '%verification%');

      if (error) {
        newStatus.database = 'error';
        newStatus.errors.push(`Database error: ${error.message}`);
      } else {
        newStatus.database = 'connected';
        newStatus.tables = tables?.map(t => t.table_name) || [];
      }
    } catch (error) {
      newStatus.database = 'error';
      newStatus.errors.push(`Database connection failed: ${error}`);
    }

    // Check Cloudinary configuration
    try {
      const cloudinaryConfig = {
        cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME
        // apiKey: import.meta.env.VITE_CLOUDINARY_API_KEY // Removed for security
      };

      if (cloudinaryConfig.cloudName) {
        newStatus.cloudinary = 'configured';
      } else {
        newStatus.cloudinary = 'missing';
        newStatus.errors.push('Cloudinary cloudName environment variable missing');
      }
    } catch (error) {
      newStatus.cloudinary = 'missing';
      newStatus.errors.push(`Cloudinary config error: ${error}`);
    }

    // Check Netlify functions
    try {
      const response = await fetch('/.netlify/functions/generate-cloudinary-signature', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: 'test', folder: 'test' })
      });

      if (response.status === 200 || response.status === 400) {
        // 400 is expected for test data, means function is deployed
        newStatus.netlifyFunctions = 'deployed';
      } else {
        newStatus.netlifyFunctions = 'missing';
        newStatus.errors.push('Netlify functions not responding');
      }
    } catch (error) {
      newStatus.netlifyFunctions = 'missing';
      newStatus.errors.push(`Netlify functions error: ${error}`);
    }

    setStatus(newStatus);
  };

  const getStatusIcon = (statusValue: string) => {
    switch (statusValue) {
      case 'connected':
      case 'configured':
      case 'deployed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
      case 'missing':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>;
    }
  };

  const getStatusText = (statusValue: string) => {
    switch (statusValue) {
      case 'connected': return 'Connected';
      case 'configured': return 'Configured';
      case 'deployed': return 'Deployed';
      case 'error': return 'Error';
      case 'missing': return 'Missing';
      default: return 'Checking...';
    }
  };

  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'connected':
      case 'configured':
      case 'deployed':
        return 'text-green-600';
      case 'error':
      case 'missing':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  const allSystemsReady = status.database === 'connected' && 
                          status.cloudinary === 'configured' && 
                          status.netlifyFunctions === 'deployed';

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <Shield className="h-16 w-16 text-purple-600 mr-4" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Verification System Status</h1>
              <p className="text-xl text-gray-600 mt-2">System Health Check</p>
            </div>
          </div>
        </div>

        {/* Overall Status */}
        <div className={`mb-8 p-6 rounded-lg border-2 ${
          allSystemsReady 
            ? 'bg-green-50 border-green-200' 
            : 'bg-yellow-50 border-yellow-200'
        }`}>
          <div className="flex items-center">
            {allSystemsReady ? (
              <CheckCircle className="h-8 w-8 text-green-600 mr-4" />
            ) : (
              <AlertCircle className="h-8 w-8 text-yellow-600 mr-4" />
            )}
            <div>
              <h2 className={`text-2xl font-bold ${
                allSystemsReady ? 'text-green-900' : 'text-yellow-900'
              }`}>
                {allSystemsReady ? 'System Ready' : 'System Setup Required'}
              </h2>
              <p className={`${
                allSystemsReady ? 'text-green-800' : 'text-yellow-800'
              }`}>
                {allSystemsReady 
                  ? 'All components are configured and ready for document verification'
                  : 'Some components need configuration before the system is ready'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Component Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          
          {/* Database Status */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center mb-4">
              <Database className="h-6 w-6 text-purple-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Database</h3>
            </div>
            <div className="flex items-center mb-2">
              {getStatusIcon(status.database)}
              <span className={`ml-2 font-medium ${getStatusColor(status.database)}`}>
                {getStatusText(status.database)}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Supabase connection and verification tables
            </p>
            {status.tables.length > 0 && (
              <div className="text-xs text-gray-500">
                <p className="font-medium mb-1">Tables found:</p>
                <ul className="space-y-1">
                  {status.tables.map(table => (
                    <li key={table}>• {table}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Cloudinary Status */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center mb-4">
              <Cloud className="h-6 w-6 text-purple-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Cloudinary</h3>
            </div>
            <div className="flex items-center mb-2">
              {getStatusIcon(status.cloudinary)}
              <span className={`ml-2 font-medium ${getStatusColor(status.cloudinary)}`}>
                {getStatusText(status.cloudinary)}
              </span>
            </div>
            <p className="text-sm text-gray-600">
              Secure document storage configuration
            </p>
          </div>

          {/* Netlify Functions Status */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center mb-4">
              <Settings className="h-6 w-6 text-purple-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Functions</h3>
            </div>
            <div className="flex items-center mb-2">
              {getStatusIcon(status.netlifyFunctions)}
              <span className={`ml-2 font-medium ${getStatusColor(status.netlifyFunctions)}`}>
                {getStatusText(status.netlifyFunctions)}
              </span>
            </div>
            <p className="text-sm text-gray-600">
              Netlify serverless functions for signatures
            </p>
          </div>
        </div>

        {/* Errors */}
        {status.errors.length > 0 && (
          <div className="mb-8 p-6 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="font-semibold text-red-900 mb-4 flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              Issues Found
            </h3>
            <ul className="space-y-2">
              {status.errors.map((error, index) => (
                <li key={index} className="text-red-800 text-sm">
                  • {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* User Info */}
        {user && (
          <div className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-4">Current User</h3>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Email:</strong> {user.emailAddresses[0]?.emailAddress}</p>
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Authenticated:</strong> Yes</p>
            </div>
          </div>
        )}

        {/* Next Steps */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Steps</h3>
          
          {allSystemsReady ? (
            <div className="space-y-3">
              <div className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="font-medium text-gray-900">System Ready!</p>
                  <p className="text-sm text-gray-600">
                    You can now test the document verification system.
                  </p>
                </div>
              </div>
              <div className="mt-4">
                <a
                  href="/verification-demo"
                  className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  Test Verification System
                </a>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {status.database !== 'connected' && (
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">Database Setup Required</p>
                    <p className="text-sm text-gray-600">
                      The verification tables have been created in Supabase.
                    </p>
                  </div>
                </div>
              )}
              
              {status.cloudinary !== 'configured' && (
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">Cloudinary Configuration</p>
                    <p className="text-sm text-gray-600">
                      Add CLOUDINARY_API_SECRET to your Netlify environment variables.
                    </p>
                  </div>
                </div>
              )}
              
              {status.netlifyFunctions !== 'deployed' && (
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-gray-900">Netlify Functions</p>
                    <p className="text-sm text-gray-600">
                      The functions should deploy automatically with your next git push.
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Refresh Button */}
        <div className="text-center mt-8">
          <button
            onClick={checkSystemStatus}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Refresh Status
          </button>
        </div>
      </div>
    </div>
  );
}
