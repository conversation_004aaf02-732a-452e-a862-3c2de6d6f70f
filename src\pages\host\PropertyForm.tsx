import React, { useState, useEffect } from 'react';
import { type LatLngTuple } from 'leaflet';
import { <PERSON><PERSON>, Popup } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { useNavigate, useParams } from 'react-router-dom';
import { submitProperty } from '../../lib/api/propertyApi';
import { useSupabase } from '../../providers/SupabaseProvider';
import {
  Ruler, Building, SquareIcon,
  Phone, AlertTriangle
} from 'lucide-react';
import PhotoUpload from '../../components/host/PhotoUpload';

// Import AddressLookup component
import AddressLookup from '../../components/host/AddressLookup';
// Import NoiseRestrictionSuggestions component
import NoiseRestrictionSuggestions, { NoiseRestrictionData } from '../../components/host/NoiseRestrictionSuggestions';

// Dynamically import Leaflet map to avoid SSR issues
const LeafletMap = React.lazy(() => import('../../components/LeafletMap'));
import { useForm, FormProvider, useFormContext } from 'react-hook-form';

interface BankDetails {
  accountName: string;
  bsb: string;
  accountNumber: string;
  bankName: string;
}

interface FormData {
  // Basic Info
  name: string;
  address: string;
  type: string;
  location: LatLngTuple;
  phoneNumber: string;
  partyAcknowledgment: boolean;
  email: string; // Added email field

  // Venue Details
  description: string;
  size: number; // Size in square metres
  functionRooms: number; // Number of function rooms (optional)
  eventSpaces: number; // Number of event spaces
  maxGuests: number;
  price: number;
  cleaningFee?: number; // Added from previous version
  hasCleaningFee: boolean; // Added from previous version
  securityDeposit?: number; // Added from previous version
  hasSecurityDeposit: boolean; // Added from previous version
  hourlyRate?: number; // Added from previous version
  dailyRate?: number; // Added from previous version
  weekendRate?: number; // Added from previous version
  hasSpecialRates: boolean; // Added from previous version


  // Amenities & Features
  amenities: string[];
  parkingDetails: string;
  transportDetails: string;
  nearbyLandmarks: string;
  byoPolicy: string;

  // House Rules
  noiseRestrictions: string;
  endTime: {
    weekday: string;
    weekend: string;
    holiday?: string; // Added for holiday end time
  };
  decorationsPolicy: string;
  smokingPolicy: string;
  petPolicy: string;
  additionalFees: string;
  curfew: {
    weekday: {
      start: string;
      end: string;
    };
    weekend: {
      start: string;
      end: string;
    };
    holiday?: { // Added for holiday curfew
      start: string;
      end: string;
    };
  };
  bassRestriction: {
    weekday: string;
    weekend: string;
    holiday?: string; // Added for holiday bass restriction
  };
  outdoorCutoff: {
    weekday: string;
    weekend: string;
    holiday?: string; // Added for holiday outdoor cutoff
  };
  specialCondition: string;
  weekdaysEndTime?: string; // Added from EnhancedPropertyForm
  weekendEndTime?: string; // Added from EnhancedPropertyForm
  holidayEndTime?: string; // Added for holiday end time
  noEndTime?: boolean; // Added from EnhancedPropertyForm
  isHoliday?: boolean; // Added for holiday detection
  holidayName?: string; // Added for holiday name
  partyScore?: { // Added for party score
    score: number;
    band: string;
    color: string;
  };


  // Insurance & Compliance
  hasInsurance: boolean;
  insuranceProvider: string;
  policyNumber: string;
  coverageAmount: string;
  expiryDate: string;
  insuranceCertificate: string;

  // Licenses & Permits
  hasLiquorLicense: boolean;
  liquorLicenseNumber: string;
  liquorLicenseExpiry: string;
  hasFoodPermit: boolean;
  foodPermitNumber: string;
  hasEntertainmentLicense: boolean;
  entertainmentLicenseNumber: string;
  capacityCertificate: string;
  fireSafetyCompliance: boolean;

  // Additional Information
  accessibilityFeatures: string[];
  cateringOptions: string;
  equipmentProvided: string;
  staffAvailable: boolean;
  setupTime: number;
  cleanupTime: number;

  // Photos
  images: string[]; // Array of image URLs

  // Bank Details
  bankDetails: BankDetails;

  // Form State
  currentStep: number;
  isDraft: boolean;
  ownerId: string; // Added ownerId field
  status: "pending" | "approved" | "rejected"; // Added status field
}

export default function PropertyForm() {
  const { userProfile } = useSupabase();
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;

  const methods = useForm<FormData>({
    defaultValues: {
      // Basic Info
      name: '',
      address: '',
      type: 'house',
      location: [-33.8688, 151.2093], // Default to Sydney
      phoneNumber: '',
      partyAcknowledgment: false,
      email: '', // Default value for email

      // Venue Details
      description: '',
      size: 0, // Size in square metres
      functionRooms: 0, // Number of function rooms (optional)
      eventSpaces: 0, // Number of event spaces
      maxGuests: 1,
      price: 0,
      cleaningFee: undefined,
      hasCleaningFee: false,
      securityDeposit: undefined,
      hasSecurityDeposit: false,
      hourlyRate: undefined,
      dailyRate: undefined,
      weekendRate: undefined,
      hasSpecialRates: false,


      // Amenities & Features
      amenities: [],
      parkingDetails: '',
      transportDetails: '',
      nearbyLandmarks: '',
      byoPolicy: '',

      // House Rules
      noiseRestrictions: '',
      endTime: {
        weekday: '',
        weekend: '',
        holiday: ''
      },
      decorationsPolicy: '',
      smokingPolicy: '',
      petPolicy: '',
      additionalFees: '',
      curfew: {
        weekday: {
          start: '',
          end: ''
        },
        weekend: {
          start: '',
          end: ''
        },
        holiday: {
          start: '',
          end: ''
        }
      },
      bassRestriction: {
        weekday: '',
        weekend: '',
        holiday: ''
      },
      outdoorCutoff: {
        weekday: '',
        weekend: '',
        holiday: ''
      },
      specialCondition: '',
      weekdaysEndTime: '', // Default value for weekdaysEndTime
      weekendEndTime: '', // Default value for weekendEndTime
      holidayEndTime: '', // Default value for holidayEndTime
      noEndTime: false, // Default value for noEndTime
      isHoliday: false, // Default value for isHoliday
      holidayName: '', // Default value for holidayName
      partyScore: {
        score: 0,
        band: '',
        color: ''
      },

      // Insurance & Compliance
      hasInsurance: false,
      insuranceProvider: '',
      policyNumber: '',
      coverageAmount: '',
      expiryDate: '',
      insuranceCertificate: '',

      // Licenses & Permits
      hasLiquorLicense: false,
      liquorLicenseNumber: '',
      liquorLicenseExpiry: '',
      hasFoodPermit: false,
      foodPermitNumber: '',
      hasEntertainmentLicense: false,
      entertainmentLicenseNumber: '',
      capacityCertificate: '',
      fireSafetyCompliance: false,

      // Additional Information
      accessibilityFeatures: [],
      cateringOptions: '',
      equipmentProvided: '',
      staffAvailable: false,
      setupTime: 0,
      cleanupTime: 0,

      // Photos
      images: [], // Array of image URLs

      // Bank Details
      bankDetails: {
        accountName: '',
        bsb: '',
        accountNumber: '',
        bankName: ''
      },

      // Form State
      currentStep: 0,
      isDraft: false,
      ownerId: userProfile?.id || '', // Default value for ownerId
      status: 'pending' // Default value for status
    }
  });

  const { handleSubmit, setValue, formState: { errors } } = methods;

  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(0); // Manage step locally

  // Define form steps
  const formSteps = [
    { id: 'basic', title: 'Basic Information' },
    { id: 'details', title: 'Venue Details' },
    { id: 'amenities', title: 'Amenities & Features' },
    { id: 'rules', title: 'House Rules' },
    { id: 'insurance', title: 'Insurance & Compliance' },
    { id: 'licenses', title: 'Licenses & Permits' },
    { id: 'additional', title: 'Additional Information' },
    { id: 'photos', title: 'Photos' },
    { id: 'bank', title: 'Bank Details' },
    { id: 'review', title: 'Review & Submit' }
  ];

  // Handle map click to update location
  const handleMapClick = (e: any) => {
    const { lat, lng } = e.latlng;
    setValue('location', [lat, lng] as LatLngTuple); // Use setValue from react-hook-form
  };

  // Navigate to next step
  const handleNext = async () => { // Renamed to handleNext
    // Optional: Add validation before moving to the next step
    // Trigger validation for fields in the current step
    let isValid = true;
    switch (currentStep) {
      case 0: // Basic Information
        isValid = await methods.trigger(['name', 'address', 'phoneNumber', 'partyAcknowledgment', 'email', 'location']);
        break;
      case 1: // Venue Details
         isValid = await methods.trigger(['size', 'eventSpaces', 'maxGuests', 'price']);
         break;
      // Add cases for other steps as needed
      default:
        isValid = true; // No validation for other steps yet
    }


    if (isValid) {
       setCurrentStep(currentStep + 1);
       window.scrollTo({ top: 0, behavior: 'smooth' });
    } else {
      // Handle validation errors (e.g., scroll to first error)
      console.log("Validation errors:", errors);
       window.scrollTo({ top: 0, behavior: 'smooth' }); // Scroll to top to show errors
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Note: goToStep function removed as it was unused

  // Save form as draft
  const saveDraft = () => {
    const currentFormData = methods.getValues(); // Get current form data
    localStorage.setItem('propertyFormDraft', JSON.stringify(currentFormData));

    setSuccess(true);
    setError('');

    setTimeout(() => {
      navigate('/host/properties');
    }, 2000);
  };

  // Load draft from localStorage
  useEffect(() => {
    const savedDraft = localStorage.getItem('propertyFormDraft');
    if (savedDraft && !isEditing) {
      try {
        const parsedDraft = JSON.parse(savedDraft);
        // Set form data using setValue from react-hook-form
        for (const key in parsedDraft) {
          if (parsedDraft.hasOwnProperty(key)) {
            setValue(key as keyof FormData, parsedDraft[key]);
          }
        }
        // Set current step from draft if available, otherwise default to 0
        setCurrentStep(parsedDraft.currentStep || 0);
      } catch (err) {
        console.error('Error parsing saved draft:', err);
      }
    }
  }, [isEditing, setValue]); // Add setValue to dependency array

  // Validate Australian phone number (can be integrated with react-hook-form validation)
  const validatePhoneNumber = (phone: string) => {
    return /^(?:\+?61|0)[2-478](?:[ -]?[0-9]){8}$/.test(phone);
  };

  // Validate BSB format (6 digits) (can be integrated with react-hook-form validation)
  const validateBSB = (bsb: string) => {
    return /^\d{6}$/.test(bsb);
  };

  // Validate account number (6-10 digits) (can be integrated with react-hook-form validation)
  const validateAccountNumber = (accountNumber: string) => {
    return /^\d{6,10}$/.test(accountNumber);
  };

  // Note: handleAmenityToggle function removed as it was unused

  // Handle images uploaded (adapt to use setValue)
  const handleImagesUploaded = (imageUrls: string[]) => {
    const currentImages = methods.getValues('images') || [];
    setValue('images', [...currentImages, ...imageUrls]);
  };

  const onSubmit = async (data: FormData) => {
    setSubmitting(true);
    setError('');

    try {
      // Ensure location is exactly [number, number] as required by the API
      const location: [number, number] = [data.location[0], data.location[1]];

      const submissionData = {
        ...data, // Include all form data
        location, // Override location with the correct format
        ownerId: userProfile?.id || '', // Ensure ownerId is from userProfile
        status: data.status || 'pending', // Ensure status is set
        // Transform endTime based on noEndTime checkbox
        endTime: data.noEndTime
          ? undefined
          : {
              weekday: data.weekdaysEndTime || '',
              weekend: data.weekendEndTime || ''
            }
      };

      // Remove fields not expected by the backend if necessary
      delete (submissionData as any).currentStep;
      delete (submissionData as any).isDraft;
      delete (submissionData as any).weekdaysEndTime; // These are merged into endTime
      delete (submissionData as any).weekendEndTime; // These are merged into endTime
      delete (submissionData as any).holidayEndTime; // These are merged into endTime
      delete (submissionData as any).noEndTime; // This controls the structure of endTime

      // Transform endTime to include holiday if available
      if (data.holidayEndTime && submissionData.endTime) {
        (submissionData.endTime as any).holiday = data.holidayEndTime;
      }


      await submitProperty(submissionData);

      // Clear draft
      localStorage.removeItem('propertyFormDraft');
      setSuccess(true);

      // Show success message and redirect after a delay
      window.scrollTo({ top: 0, behavior: 'smooth' });
      setTimeout(() => navigate('/host/properties'), 2000);
    } catch (err) {
      setError('Failed to submit venue. Please try again.');
      console.error('Submission error:', err);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setSubmitting(false);
    }
  };

  // Render Basic Info section
  const renderBasicInfo = () => {
    const { register, formState: { errors }, watch, setValue } = useFormContext(); // Use useFormContext
    const location = watch('location'); // Watch location for map marker

    return (
      <div className="border-b pb-6 mb-6">
        <h2 className="text-xl font-bold mb-4">Basic Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 font-medium">Property Name*</label>
            <input
              type="text"
              {...register('name', { required: 'Property Name is required' })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
          </div>
           <div>
            <label className="block mb-2 font-medium">Email*</label>
            <input
              type="email"
              {...register('email', { required: 'Email is required' })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium">Australian Phone Number*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Phone className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="tel"
                {...register('phoneNumber', {
                  required: 'Phone Number is required',
                  validate: value => validatePhoneNumber(value) || 'Invalid Australian phone number'
                })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.phoneNumber && <p className="text-red-500 text-sm">{errors.phoneNumber.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Property Type*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Building className="h-5 w-5 text-gray-400" />
              </div>
              <select
                {...register('type', { required: 'Property Type is required' })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="house">House</option>
                <option value="apartment">Apartment</option>
                <option value="venue">Venue</option>
                <option value="other">Other</option>
              </select>
              {errors.type && <p className="text-red-500 text-sm">{errors.type.message}</p>}
            </div>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Address*</label>
            <AddressLookup
              value={watch('address')} // Provide value prop
              onChange={(address: string, latlng: [number, number]) => { // Corrected prop and added types
               setValue('address', address);
               setValue('location', latlng as LatLngTuple); // Ensure correct type for location
             }} />
            <input
              type="text"
              {...register('address', { required: 'Address is required' })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 mt-2"
              placeholder="Enter address or select from suggestions"
              readOnly // Make address input read-only as it's set by AddressLookup
            />
            {errors.address && <p className="text-red-500 text-sm">{errors.address.message}</p>}
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Location on Map*</label>
            <div className="h-64 border rounded overflow-hidden">
              <React.Suspense fallback={<div className="h-64 bg-gray-100 rounded flex items-center justify-center">Loading map...</div>}>
                {location && location.length === 2 && ( // Only render map if location is valid
                  <LeafletMap
                    center={location}
                    zoom={13}
                    onClick={handleMapClick} // Corrected prop name
                  >
                    <Marker position={location}>
                      <Popup>
                        Venue Location
                      </Popup>
                    </Marker>
                  </LeafletMap>
                )}
              </React.Suspense>
            </div>
             <input // Hidden input to include location in form data and validation
              type="hidden"
              {...register('location', {
                required: 'Location is required',
                validate: value => Array.isArray(value) && value.length === 2 || 'Invalid location format'
              })}
            />
            {errors.location && <p className="text-red-500 text-sm">{errors.location.message}</p>}
            <p className="mt-2 text-sm text-gray-600">Click on the map to set the exact location.</p>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Description*</label>
            <textarea
              {...register('description', { required: 'Description is required' })}
              rows={4}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
            {errors.description && <p className="text-red-500 text-sm">{errors.description.message}</p>}
          </div>
          <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('partyAcknowledgment', { required: 'Party Acknowledgment is required' })}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                I acknowledge that HouseGoing is a party-focused platform and my venue may be used for parties and events.*
              </label>
            </div>
             {errors.partyAcknowledgment && <p className="text-red-500 text-sm">{errors.partyAcknowledgment.message}</p>}
          </div>
        </div>
      </div>
    );
  };

  // Render Venue Details section
  const renderVenueDetails = () => {
     const { register, formState: { errors }, watch } = useFormContext(); // Use useFormContext
     const hasCleaningFee = watch('hasCleaningFee');
     const hasSecurityDeposit = watch('hasSecurityDeposit');
     const hasSpecialRates = watch('hasSpecialRates');

    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Venue Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 font-medium">Size (square metres)*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <SquareIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="number"
                {...register('size', { required: 'Size is required', valueAsNumber: true, min: { value: 1, message: 'Size must be at least 1' } })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.size && <p className="text-red-500 text-sm">{errors.size.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Function Rooms</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Building className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="number"
                {...register('functionRooms', { valueAsNumber: true, min: { value: 0, message: 'Function rooms cannot be negative' } })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
               {errors.functionRooms && <p className="text-red-500 text-sm">{errors.functionRooms.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Event Spaces*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Building className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="number"
                {...register('eventSpaces', { required: 'Event Spaces is required', valueAsNumber: true, min: { value: 1, message: 'Event spaces must be at least 1' } })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.eventSpaces && <p className="text-red-500 text-sm">{errors.eventSpaces.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Maximum Guests*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Ruler className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="number"
                {...register('maxGuests', { required: 'Maximum Guests is required', valueAsNumber: true, min: { value: 1, message: 'Maximum guests must be at least 1' } })}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.maxGuests && <p className="text-red-500 text-sm">{errors.maxGuests.message}</p>}
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Hourly Rate (AUD)*</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                {...register('price', { required: 'Hourly Rate is required', valueAsNumber: true, min: { value: 0, message: 'Hourly rate cannot be negative' } })}
                className="block w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.price && <p className="text-red-500 text-sm">{errors.price.message}</p>}
            </div>
          </div>

           <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasCleaningFee')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Add cleaning fee
              </label>
            </div>

            {hasCleaningFee && (
              <div className="mt-2">
                <label className="block text-sm font-medium text-gray-700">
                  Cleaning Fee (AUD)
                </label>
                <input
                  type="number"
                  {...register('cleaningFee', { valueAsNumber: true, min: { value: 0, message: 'Cleaning fee cannot be negative' } })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
                 {errors.cleaningFee && <p className="text-red-500 text-sm">{errors.cleaningFee.message}</p>}
                <p className="mt-1 text-sm text-gray-500">
                  This fee will be added to the total booking cost
                </p>
              </div>
            )}
          </div>

           <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasSecurityDeposit')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Add security deposit
              </label>
            </div>

            {hasSecurityDeposit && (
              <div className="mt-2">
                <label className="block text-sm font-medium text-gray-700">
                  Security Deposit (AUD)
                </label>
                <input
                  type="number"
                  {...register('securityDeposit', { valueAsNumber: true, min: { value: 0, message: 'Security deposit cannot be negative' } })}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
                 {errors.securityDeposit && <p className="text-red-500 text-sm">{errors.securityDeposit.message}</p>}
                <p className="mt-1 text-sm text-gray-500">
                  This deposit will be held and released after the booking
                </p>
              </div>
            )}
          </div>

           <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasSpecialRates')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Add special rates (hourly, daily, weekend)
              </label>
            </div>

            {hasSpecialRates && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Hourly Rate (AUD)
                  </label>
                  <input
                    type="number"
                    {...register('hourlyRate', { valueAsNumber: true, min: { value: 0, message: 'Hourly rate cannot be negative' } })}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                   {errors.hourlyRate && <p className="text-red-500 text-sm">{errors.hourlyRate.message}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Daily Rate (AUD)
                  </label>
                  <input
                    type="number"
                    {...register('dailyRate', { valueAsNumber: true, min: { value: 0, message: 'Daily rate cannot be negative' } })}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                   {errors.dailyRate && <p className="text-red-500 text-sm">{errors.dailyRate.message}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Weekend Rate (AUD)
                  </label>
                  <input
                    type="number"
                    {...register('weekendRate', { valueAsNumber: true, min: { value: 0, message: 'Weekend rate cannot be negative' } })}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                   {errors.weekendRate && <p className="text-red-500 text-sm">{errors.weekendRate.message}</p>}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render Amenities & Features section
  const renderAmenities = () => {
     const { register } = useFormContext(); // Use useFormContext
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Amenities & Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Amenity checkboxes */}
          {/* Example: */}
          {/* <div>
            <input type="checkbox" id="pool" {...register('amenities')} value="pool" />
            <label htmlFor="pool">Pool</label>
          </div> */}
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Parking Details</label>
            <textarea
              {...register('parkingDetails')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Transport Details</label>
            <textarea
              {...register('transportDetails')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Nearby Landmarks</label>
            <textarea
              {...register('nearbyLandmarks')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">BYO Policy</label>
            <textarea
              {...register('byoPolicy')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
        </div>
      </div>
    );
  };

  // Render House Rules section
  const renderHouseRules = () => {
     const { register, watch, setValue } = useFormContext(); // Use useFormContext with setValue
     const noEndTime = watch('noEndTime');
     const address = watch('address');
     const location = watch('location');

     // Handle applying noise restriction suggestions
     const handleApplySuggestions = (suggestions: NoiseRestrictionData) => {
       setValue('noiseRestrictions', suggestions.noiseRestrictions);
       setValue('weekdaysEndTime', suggestions.weekdaysEndTime);
       setValue('weekendEndTime', suggestions.weekendEndTime);

       // Set holiday end time if available
       if (suggestions.holidayEndTime) {
         setValue('holidayEndTime', suggestions.holidayEndTime);
       }

       // Set weekday curfew
       setValue('curfew.weekday.start', suggestions.curfew.weekday.start);
       setValue('curfew.weekday.end', suggestions.curfew.weekday.end);

       // Set weekend curfew
       setValue('curfew.weekend.start', suggestions.curfew.weekend.start);
       setValue('curfew.weekend.end', suggestions.curfew.weekend.end);

       // Set holiday curfew if available
       if (suggestions.curfew.holiday) {
         if (!watch('curfew.holiday')) {
           // Initialize the holiday object if it doesn't exist
           setValue('curfew.holiday', { start: '', end: '' });
         }
         setValue('curfew.holiday.start', suggestions.curfew.holiday.start);
         setValue('curfew.holiday.end', suggestions.curfew.holiday.end);
       }

       // Set bass restrictions
       setValue('bassRestriction.weekday', suggestions.bassRestriction.weekday);
       setValue('bassRestriction.weekend', suggestions.bassRestriction.weekend);

       // Set holiday bass restriction if available
       if (suggestions.bassRestriction.holiday) {
         setValue('bassRestriction.holiday', suggestions.bassRestriction.holiday);
       }

       // Set outdoor cutoffs
       setValue('outdoorCutoff.weekday', suggestions.outdoorCutoff.weekday);
       setValue('outdoorCutoff.weekend', suggestions.outdoorCutoff.weekend);

       // Set holiday outdoor cutoff if available
       if (suggestions.outdoorCutoff.holiday) {
         setValue('outdoorCutoff.holiday', suggestions.outdoorCutoff.holiday);
       }

       // Set special condition
       setValue('specialCondition', suggestions.specialCondition);

       // Set holiday information if available
       if (suggestions.isHoliday !== undefined) {
         setValue('isHoliday', suggestions.isHoliday);
       }

       if (suggestions.holidayName) {
         setValue('holidayName', suggestions.holidayName);
       }

       // Set party score if available
       if (suggestions.partyScore) {
         setValue('partyScore', suggestions.partyScore);
       }
     };

    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">House Rules</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Noise Restrictions</label>
            <textarea
              {...register('noiseRestrictions')}
              rows={3}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe any noise restrictions for your venue"
            ></textarea>

            {/* NSW Party Planning Tool Integration */}
            {address && location && (
              <div className="mt-2">
                <NoiseRestrictionSuggestions
                  address={address}
                  coordinates={location}
                  onApplySuggestions={handleApplySuggestions}
                />
              </div>
            )}
          </div>
          {!noEndTime && (
            <>
              <div>
                <label className="block mb-2 font-medium">Latest End Time (Weekday)</label>
                <input
                  type="time"
                  {...register('weekdaysEndTime')} // Use weekdaysEndTime for input
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Latest End Time (Weekend)</label>
                <input
                  type="time"
                  {...register('weekendEndTime')} // Use weekendEndTime for input
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Latest End Time (Public Holiday)</label>
                <input
                  type="time"
                  {...register('holidayEndTime')} // Use holidayEndTime for input
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </>
          )}
           <div className="md:col-span-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('noEndTime')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                No End Time (Activities Allowed)
              </label>
            </div>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Decorations Policy</label>
            <textarea
              {...register('decorationsPolicy')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe any restrictions on decorations (e.g., no confetti, wall attachments, etc.)"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Smoking Policy</label>
            <textarea
              {...register('smokingPolicy')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe your smoking policy (e.g., no smoking indoors, designated areas, etc.)"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Pet Policy</label>
            <textarea
              {...register('petPolicy')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe your pet policy (e.g., no pets allowed, service animals only, etc.)"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Additional Fees</label>
            <textarea
              {...register('additionalFees')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Describe any additional fees (e.g., cleaning fee, damage deposit, etc.)"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Special Conditions</label>
            <textarea
              {...register('specialCondition')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="Any other special conditions or rules guests should be aware of"
            ></textarea>
          </div>
        </div>
      </div>
    );
  };

  // Render Insurance & Compliance section
  const renderInsurance = () => {
     const { register, watch } = useFormContext(); // Use useFormContext
     const hasInsurance = watch('hasInsurance');
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Insurance & Compliance</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasInsurance')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                I have public liability insurance
              </label>
            </div>
          </div>
          {hasInsurance && (
            <>
              <div>
                <label className="block mb-2 font-medium">Insurance Provider</label>
                <input
                  type="text"
                  {...register('insuranceProvider')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Policy Number</label>
                <input
                  type="text"
                  {...register('policyNumber')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Coverage Amount</label>
                <input
                  type="text"
                  {...register('coverageAmount')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Expiry Date</label>
                <input
                  type="date"
                  {...register('expiryDate')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block mb-2 font-medium">Insurance Certificate (Upload)</label>
                {/* File upload component would go here */}
                <input
                  type="file"
                  onChange={(e) => {
                    // Handle file upload
                    console.log(e.target.files);
                  }}
                  className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                />
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  // Render Licenses & Permits section
  const renderLicenses = () => {
     const { register, watch } = useFormContext(); // Use useFormContext
     const hasLiquorLicense = watch('hasLiquorLicense');
     const hasFoodPermit = watch('hasFoodPermit');
     const hasEntertainmentLicense = watch('hasEntertainmentLicense');
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Licenses & Permits</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasLiquorLicense')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                I have a liquor license
              </label>
            </div>
          </div>
          {hasLiquorLicense && (
            <>
              <div>
                <label className="block mb-2 font-medium">Liquor License Number</label>
                <input
                  type="text"
                  {...register('liquorLicenseNumber')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block mb-2 font-medium">Liquor License Expiry Date</label>
                <input
                  type="date"
                  {...register('liquorLicenseExpiry')}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </>
          )}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasFoodPermit')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                I have a food permit
              </label>
            </div>
          </div>
          {hasFoodPermit && (
            <div>
              <label className="block mb-2 font-medium">Food Permit Number</label>
              <input
                type="text"
                {...register('foodPermitNumber')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          )}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('hasEntertainmentLicense')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                I have an entertainment license
              </label>
            </div>
          </div>
          {hasEntertainmentLicense && (
            <div>
              <label className="block mb-2 font-medium">Entertainment License Number</label>
              <input
                type="text"
                {...register('entertainmentLicenseNumber')}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          )}
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Capacity Certificate (Upload)</label>
            {/* File upload component would go here */}
            <input
              type="file"
              onChange={(e) => {
                // Handle file upload
                console.log(e.target.files);
              }}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('fireSafetyCompliance')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Venue complies with fire safety regulations
              </label>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render Additional Information section
  const renderAdditionalInfo = () => {
     const { register } = useFormContext(); // Use useFormContext
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Additional Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Accessibility Features</label>
            {/* Accessibility feature checkboxes */}
            {/* Example: */}
            {/* <div>
              <input type="checkbox" id="wheelchair" {...register('accessibilityFeatures')} value="wheelchair" />
              <label htmlFor="wheelchair">Wheelchair Accessible</label>
            </div> */}
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Catering Options</label>
            <textarea
              {...register('cateringOptions')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Equipment Provided</label>
            <textarea
              {...register('equipmentProvided')}
              rows={2}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            ></textarea>
          </div>
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                {...register('staffAvailable')}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label className="ml-2 block text-sm text-gray-700">
                Staff available for events
              </label>
            </div>
          </div>
          <div>
            <label className="block mb-2 font-medium">Setup Time (hours)</label>
            <input
              type="number"
              {...register('setupTime', { valueAsNumber: true, min: { value: 0, message: 'Setup time cannot be negative' } })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.setupTime && <p className="text-red-500 text-sm">{errors.setupTime.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium">Cleanup Time (hours)</label>
            <input
              type="number"
              {...register('cleanupTime', { valueAsNumber: true, min: { value: 0, message: 'Cleanup time cannot be negative' } })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.cleanupTime && <p className="text-red-500 text-sm">{errors.cleanupTime.message}</p>}
          </div>
        </div>
      </div>
    );
  };

  // Render Photos section
  const renderPhotos = () => {
     const { watch } = useFormContext(); // Use useFormContext
     const images = watch('images') || [];
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Photos</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className="block mb-2 font-medium">Upload Photos</label>
            <PhotoUpload onImagesUploaded={handleImagesUploaded} />
            {/* Display uploaded images */}
            {images.length > 0 && (
              <div className="mt-4 grid grid-cols-3 gap-4">
                {images.map((imageUrl: string, index: number) => ( // Added type annotations
                  <img key={index} src={imageUrl} alt={`Venue Photo ${index + 1}`} className="w-full h-auto rounded-md" />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render Bank Details section
  const renderBankDetails = () => {
     const { register, formState: { errors } } = useFormContext(); // Use useFormContext
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Bank Details</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block mb-2 font-medium">Account Name*</label>
            <input
              type="text"
              {...register('bankDetails.accountName', { required: 'Account Name is required' })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.bankDetails?.accountName && <p className="text-red-500 text-sm">{errors.bankDetails.accountName.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium">BSB*</label>
            <input
              type="text"
              {...register('bankDetails.bsb', {
                required: 'BSB is required',
                validate: value => validateBSB(value) || 'Invalid BSB format (6 digits)'
              })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.bankDetails?.bsb && <p className="text-red-500 text-sm">{errors.bankDetails.bsb.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium">Account Number*</label>
            <input
              type="text"
              {...register('bankDetails.accountNumber', {
                required: 'Account Number is required',
                validate: value => validateAccountNumber(value) || 'Invalid account number (6-10 digits)'
              })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.bankDetails?.accountNumber && <p className="text-red-500 text-sm">{errors.bankDetails.accountNumber.message}</p>}
          </div>
          <div>
            <label className="block mb-2 font-medium">Bank Name*</label>
            <input
              type="text"
              {...register('bankDetails.bankName', { required: 'Bank Name is required' })}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
             {errors.bankDetails?.bankName && <p className="text-red-500 text-sm">{errors.bankDetails.bankName.message}</p>}
          </div>
        </div>
      </div>
    );
  };

  // Render Review & Submit section
  const renderReview = () => {
     const { watch } = useFormContext(); // Use useFormContext
     const formData = watch(); // Get all form data for review
    return (
      <div className="border-t pt-6">
        <h2 className="text-xl font-bold mb-4">Review & Submit</h2>
        {/* Display summary of form data for review */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">Basic Information</h3>
            <p><strong>Property Name:</strong> {formData.name}</p>
            <p><strong>Address:</strong> {formData.address}</p>
            <p><strong>Property Type:</strong> {formData.type}</p>
            <p><strong>Email:</strong> {formData.email}</p>
            <p><strong>Phone Number:</strong> {formData.phoneNumber}</p>
            <p><strong>Description:</strong> {formData.description}</p>
            <p><strong>Party Acknowledgment:</strong> {formData.partyAcknowledgment ? 'Yes' : 'No'}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold">Venue Details</h3>
            <p><strong>Size:</strong> {formData.size} sqm</p>
            <p><strong>Function Rooms:</strong> {formData.functionRooms}</p>
            <p><strong>Event Spaces:</strong> {formData.eventSpaces}</p>
            <p><strong>Maximum Guests:</strong> {formData.maxGuests}</p>
            <p><strong>Hourly Rate:</strong> ${formData.price}</p>
             {formData.hasCleaningFee && <p><strong>Cleaning Fee:</strong> ${formData.cleaningFee}</p>}
             {formData.hasSecurityDeposit && <p><strong>Security Deposit:</strong> ${formData.securityDeposit}</p>}
             {formData.hasSpecialRates && (
               <>
                 <p><strong>Hourly Special Rate:</strong> ${formData.hourlyRate}</p>
                 <p><strong>Daily Special Rate:</strong> ${formData.dailyRate}</p>
                 <p><strong>Weekend Special Rate:</strong> ${formData.weekendRate}</p>
               </>
             )}
          </div>
           <div>
             <h3 className="text-lg font-semibold">Amenities & Features</h3>
             <p><strong>Amenities:</strong> {formData.amenities.join(', ') || 'None'}</p>
             <p><strong>Parking Details:</strong> {formData.parkingDetails || 'N/A'}</p>
             <p><strong>Transport Details:</strong> {formData.transportDetails || 'N/A'}</p>
             <p><strong>Nearby Landmarks:</strong> {formData.nearbyLandmarks || 'N/A'}</p>
             <p><strong>BYO Policy:</strong> {formData.byoPolicy || 'N/A'}</p>
           </div>
           <div>
             <h3 className="text-lg font-semibold">House Rules</h3>
             <p><strong>Noise Restrictions:</strong> {formData.noiseRestrictions || 'N/A'}</p>
             {formData.noEndTime ? (
               <p><strong>Latest End Time:</strong> No End Time (Activities Allowed)</p>
             ) : (
               <>
                 <p><strong>Latest End Time (Weekday):</strong> {formData.weekdaysEndTime || 'N/A'}</p>
                 <p><strong>Latest End Time (Weekend):</strong> {formData.weekendEndTime || 'N/A'}</p>
                 <p><strong>Latest End Time (Public Holiday):</strong> {formData.holidayEndTime || 'N/A'}</p>
               </>
             )}

             {formData.partyScore && formData.partyScore.score > 0 && (
               <p><strong>Party Score:</strong> {formData.partyScore.score}/10 - {formData.partyScore.band}</p>
             )}
             <p><strong>Decorations Policy:</strong> {formData.decorationsPolicy || 'N/A'}</p>
             <p><strong>Smoking Policy:</strong> {formData.smokingPolicy || 'N/A'}</p>
             <p><strong>Pet Policy:</strong> {formData.petPolicy || 'N/A'}</p>
             <p><strong>Additional Fees:</strong> {formData.additionalFees || 'N/A'}</p>
             {/* Add review for Curfew, Bass Restriction, Outdoor Cutoff */}
             <p><strong>Special Conditions:</strong> {formData.specialCondition || 'N/A'}</p>
           </div>
            <div>
              <h3 className="text-lg font-semibold">Insurance & Compliance</h3>
              <p><strong>Public Liability Insurance:</strong> {formData.hasInsurance ? 'Yes' : 'No'}</p>
              {formData.hasInsurance && (
                <>
                  <p><strong>Insurance Provider:</strong> {formData.insuranceProvider || 'N/A'}</p>
                  <p><strong>Policy Number:</strong> {formData.policyNumber || 'N/A'}</p>
                  <p><strong>Coverage Amount:</strong> {formData.coverageAmount || 'N/A'}</p>
                  <p><strong>Expiry Date:</strong> {formData.expiryDate || 'N/A'}</p>
                  {/* Indicate if insurance certificate is uploaded */}
                </>
              )}
            </div>
             <div>
               <h3 className="text-lg font-semibold">Licenses & Permits</h3>
               <p><strong>Liquor License:</strong> {formData.hasLiquorLicense ? 'Yes' : 'No'}</p>
               {formData.hasLiquorLicense && (
                 <>
                   <p><strong>Liquor License Number:</strong> {formData.liquorLicenseNumber || 'N/A'}</p>
                   <p><strong>Liquor License Expiry:</strong> {formData.liquorLicenseExpiry || 'N/A'}</p>
                 </>
               )}
               <p><strong>Food Permit:</strong> {formData.hasFoodPermit ? 'Yes' : 'No'}</p>
               {formData.hasFoodPermit && <p><strong>Food Permit Number:</strong> {formData.foodPermitNumber || 'N/A'}</p>}
               <p><strong>Entertainment License:</strong> {formData.hasEntertainmentLicense ? 'Yes' : 'No'}</p>
               {formData.hasEntertainmentLicense && <p><strong>Entertainment License Number:</strong> {formData.entertainmentLicenseNumber || 'N/A'}</p>}
               {/* Indicate if capacity certificate is uploaded */}
               <p><strong>Fire Safety Compliance:</strong> {formData.fireSafetyCompliance ? 'Yes' : 'No'}</p>
             </div>
              <div>
                <h3 className="text-lg font-semibold">Additional Information</h3>
                <p><strong>Accessibility Features:</strong> {formData.accessibilityFeatures.join(', ') || 'None'}</p>
                <p><strong>Catering Options:</strong> {formData.cateringOptions || 'N/A'}</p>
                <p><strong>Equipment Provided:</strong> {formData.equipmentProvided || 'N/A'}</p>
                <p><strong>Staff Available:</strong> {formData.staffAvailable ? 'Yes' : 'No'}</p>
                <p><strong>Setup Time:</strong> {formData.setupTime || 0} hours</p>
                <p><strong>Cleanup Time:</strong> {formData.cleanupTime || 0} hours</p>
              </div>
               <div>
                 <h3 className="text-lg font-semibold">Photos</h3>
                 <p><strong>Number of Photos Uploaded:</strong> {formData.images.length}</p>
                 {/* Could display thumbnails here if needed */}
               </div>
          <div>
            <h3 className="text-lg font-semibold">Bank Details</h3>
            <p><strong>Account Name:</strong> {formData.bankDetails.accountName}</p>
            <p><strong>BSB:</strong> {formData.bankDetails.bsb}</p>
            <p><strong>Account Number:</strong> {formData.bankDetails.accountNumber}</p>
            <p><strong>Bank Name:</strong> {formData.bankDetails.bankName}</p>
          </div>
        </div>
      </div>
    );
  };


  // Render current step content
  const renderCurrentStep = () => {
    switch(currentStep) { // Use local currentStep state
      case 0:
        return renderBasicInfo();
      case 1:
        return renderVenueDetails();
      case 2:
        return renderAmenities();
      case 3:
        return renderHouseRules();
      case 4:
        return renderInsurance();
      case 5:
        return renderLicenses();
      case 6:
        return renderAdditionalInfo();
      case 7:
        return renderPhotos();
      case 8:
        return renderBankDetails();
      case 9:
        return renderReview();
      default:
        return null;
    }
  };

  // Render step navigation buttons
  const renderStepNavigation = () => (
    <div className="flex justify-between mt-6">
      {currentStep > 0 && ( // Use local currentStep state
        <button
          type="button"
          onClick={prevStep}
          className="px-4 py-2 border rounded"
        >
          Previous
        </button>
      )}
      {currentStep < formSteps.length - 1 ? ( // Use local currentStep state
        <button
          type="button"
          onClick={handleNext} // Use handleNext for validation
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Next
        </button>
      ) : (
        <button
          type="submit"
          disabled={submitting}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
        >
          {submitting ? 'Submitting...' : 'Submit Venue'}
        </button>
      )}
    </div>
  );

  // Render progress indicators
  const renderProgressIndicators = () => (
    <div className="flex justify-between mb-8">
      {formSteps.map((step, index) => (
        <div key={step.id} className={`flex-1 text-center ${index <= currentStep ? 'text-blue-600' : 'text-gray-400'}`}> {/* Use local currentStep state */}
          <div className={`w-8 h-8 mx-auto rounded-full border-2 flex items-center justify-center font-bold ${index <= currentStep ? 'border-blue-600 bg-blue-100' : 'border-gray-400'}`}> {/* Use local currentStep state */}
            {index + 1}
          </div>
          <div className="text-sm mt-1">{step.title}</div>
        </div>
      ))}
    </div>
  );


  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">
        {isEditing ? 'Edit Venue' : 'Add New Venue'}
      </h1>

      {/* Progress Steps */}
      {renderProgressIndicators()}

      {/* Error and Success Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            {error}
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
          <p className="font-medium">Venue submitted successfully!</p>
        </div>
      )}

      <FormProvider {...methods}> {/* Wrap form with FormProvider */}
        <form onSubmit={handleSubmit(onSubmit)}> {/* Use react-hook-form's handleSubmit */}
          {renderCurrentStep()}
          {renderStepNavigation()}
        </form>
      </FormProvider>
    </div>
  );
}
