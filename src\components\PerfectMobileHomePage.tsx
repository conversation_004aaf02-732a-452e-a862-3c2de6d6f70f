import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Venue } from '../types/venue';
import { PerfectMobileSearch } from './PerfectMobileSearch';
import { PerfectMobileVenueCard } from './PerfectMobileVenueCard';
import { PerfectMobileNavigation } from './PerfectMobileNavigation';
import SEO from './seo/SEO';
import { OrganizationSchema, WebsiteSchema } from './seo/JsonLd';

export const PerfectMobileHomePage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAuthSuccess, setShowAuthSuccess] = useState(false);

  // Perfect mock venues for mobile
  const venues: Venue[] = [
    {
      id: '1',
      title: 'Sydney CBD Rooftop',
      description: 'Stunning rooftop venue with city views',
      location: 'Sydney CBD',
      price: 150,
      capacity: 50,
      rating: 4.8,
      reviews: 24,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Bathroom'],
      eventTypes: ['Birthday', 'Corporate', 'Wedding'],
      coordinates: { latitude: -33.8688, longitude: 151.2093 },
      host: { id: '1', name: 'Sarah M', image: '/placeholder.svg', rating: 4.9 }
    },
    {
      id: '2',
      title: 'Bondi Beach House',
      description: 'Beachfront property perfect for parties',
      location: 'Bondi Beach',
      price: 200,
      capacity: 30,
      rating: 4.9,
      reviews: 18,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Parking'],
      eventTypes: ['Birthday', 'Beach Party'],
      coordinates: { latitude: -33.8915, longitude: 151.2767 },
      host: { id: '2', name: 'Mike T', image: '/placeholder.svg', rating: 4.7 }
    },
    {
      id: '3',
      title: 'Newtown Warehouse',
      description: 'Industrial space with great acoustics',
      location: 'Newtown',
      price: 120,
      capacity: 80,
      rating: 4.6,
      reviews: 31,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Sound System', 'Bar'],
      eventTypes: ['Birthday', 'Live Music', 'Corporate'],
      coordinates: { latitude: -33.8969, longitude: 151.1831 },
      host: { id: '3', name: 'Alex R', image: '/placeholder.svg', rating: 4.8 }
    },
    {
      id: '4',
      title: 'Manly Penthouse',
      description: 'Luxury penthouse with harbor views',
      location: 'Manly',
      price: 300,
      capacity: 25,
      rating: 5.0,
      reviews: 12,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Balcony', 'Parking'],
      eventTypes: ['Birthday', 'Anniversary', 'Corporate'],
      coordinates: { latitude: -33.7979, longitude: 151.2845 },
      host: { id: '4', name: 'Emma L', image: '/placeholder.svg', rating: 5.0 }
    },
    {
      id: '5',
      title: 'Surry Hills Studio',
      description: 'Modern studio in trendy area',
      location: 'Surry Hills',
      price: 100,
      capacity: 40,
      rating: 4.7,
      reviews: 28,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Projector'],
      eventTypes: ['Birthday', 'Workshop', 'Meeting'],
      coordinates: { latitude: -33.8831, longitude: 151.2169 },
      host: { id: '5', name: 'James K', image: '/placeholder.svg', rating: 4.6 }
    },
    {
      id: '6',
      title: 'Darling Harbour Loft',
      description: 'Waterfront loft with city views',
      location: 'Darling Harbour',
      price: 250,
      capacity: 35,
      rating: 4.8,
      reviews: 19,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Balcony'],
      eventTypes: ['Birthday', 'Corporate', 'Wedding'],
      coordinates: { latitude: -33.8715, longitude: 151.2006 },
      host: { id: '6', name: 'Lisa M', image: '/placeholder.svg', rating: 4.9 }
    }
  ];

  // Check for auth success
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const authStatus = params.get('auth');
    if (authStatus === 'success') {
      setShowAuthSuccess(true);
      const timer = setTimeout(() => setShowAuthSuccess(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [location]);

  const handleSearch = (filters: any) => {
    navigate('/find-venues', { state: { filters } });
  };

  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <SEO
        title="Find Your Perfect Party Venue | HouseGoing"
        description="Discover party-ready venues across NSW. Find the perfect space for your next celebration."
        url="https://housegoing.com.au/"
      />
      <OrganizationSchema />
      <WebsiteSchema />

      {/* Success message */}
      {showAuthSuccess && (
        <div className="bg-green-100 text-green-700 px-4 py-2 text-sm text-center">
          Welcome back! 🎉
        </div>
      )}

      {/* Header */}
      <header className="bg-white border-b border-gray-100 px-4 py-3">
        <h1 className="text-xl font-bold text-gray-900">HouseGoing</h1>
        <p className="text-xs text-gray-600">Find your perfect party venue</p>
      </header>

      {/* Search */}
      <PerfectMobileSearch onSearch={handleSearch} />

      {/* Featured venues */}
      <div className="px-4 py-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Top Venues</h2>
          <button 
            onClick={() => navigate('/find-venues')}
            className="text-sm text-orange-600 hover:text-orange-700"
          >
            See all
          </button>
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          {venues.slice(0, 4).map((venue, index) => (
            <PerfectMobileVenueCard key={venue.id} venue={venue} index={index} />
          ))}
        </div>
      </div>

      {/* Quick actions */}
      <div className="px-4 py-4 bg-white border-t border-gray-100">
        <h3 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          <button 
            onClick={() => navigate('/find-venues')}
            className="bg-orange-500 text-white py-3 rounded-lg text-sm font-medium active:scale-95"
          >
            Browse All Venues
          </button>
          <button 
            onClick={() => navigate('/host')}
            className="border border-orange-500 text-orange-500 py-3 rounded-lg text-sm font-medium active:scale-95"
          >
            List Your Space
          </button>
        </div>
      </div>

      {/* Bottom navigation */}
      <PerfectMobileNavigation />
    </div>
  );
};
