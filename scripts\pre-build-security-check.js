#!/usr/bin/env node

/**
 * Pre-Build Security Check for HouseGoing
 * Ensures no secrets are exposed in client-side build
 */

import fs from 'fs';
import path from 'path';

console.log('🔒 Running pre-build security check...');

// Check for dangerous environment variables that shouldn't be in client build
const DANGEROUS_ENV_VARS = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'CLERK_SECRET_KEY',
  'STRIPE_SECRET_KEY',
  'CLOUDINARY_API_SECRET',
  'GMAIL_APP_PASSWORD',
  'BREVO_SMTP_PASS'
];

// Check for VITE_ prefixed secret keys (these get bundled into client)
const VITE_SECRET_PATTERNS = [
  'VITE_SUPABASE_SERVICE_ROLE_KEY',
  'VITE_CLERK_SECRET_KEY',
  'VITE_STRIPE_SECRET_KEY',
  'VITE_CLOUDINARY_API_SECRET'
];

let hasErrors = false;

// Function to scan file for dangerous patterns
function scanFileForSecrets(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];

    // Check for hardcoded secrets
    DANGEROUS_ENV_VARS.forEach(envVar => {
      const patterns = [
        new RegExp(`${envVar}\\s*=\\s*['"][^'"]+['"]`, 'gi'),
        new RegExp(`process\\.env\\.${envVar}`, 'gi'),
        new RegExp(`import\\.meta\\.env\\.${envVar}`, 'gi')
      ];

      patterns.forEach(pattern => {
        if (pattern.test(content)) {
          issues.push(`Found reference to ${envVar} in ${filePath}`);
        }
      });
    });

    // Check for VITE_ prefixed secrets
    VITE_SECRET_PATTERNS.forEach(envVar => {
      const patterns = [
        new RegExp(`${envVar}`, 'gi'),
        new RegExp(`import\\.meta\\.env\\.${envVar}`, 'gi')
      ];

      patterns.forEach(pattern => {
        if (pattern.test(content)) {
          issues.push(`CRITICAL: Found VITE_ prefixed secret ${envVar} in ${filePath} - this will be exposed in client build!`);
        }
      });
    });

    return issues;
  } catch (error) {
    return [`Error reading ${filePath}: ${error.message}`];
  }
}

// Recursively scan directory
function scanDirectory(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
  const issues = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
          issues.push(...scanDirectory(fullPath, extensions));
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          issues.push(...scanFileForSecrets(fullPath));
        }
      }
    }
  } catch (error) {
    issues.push(`Error scanning directory ${dir}: ${error.message}`);
  }
  
  return issues;
}

// Check environment variables
console.log('Checking environment variables...');
VITE_SECRET_PATTERNS.forEach(envVar => {
  if (process.env[envVar]) {
    console.error(`❌ CRITICAL: ${envVar} is set as environment variable - this will be exposed in client build!`);
    hasErrors = true;
  }
});

// Scan source code
console.log('Scanning source code for secrets...');
const sourceIssues = scanDirectory('./src');

if (sourceIssues.length > 0) {
  console.error('❌ Security issues found in source code:');
  sourceIssues.forEach(issue => console.error(`  - ${issue}`));
  hasErrors = true;
}

// Check vite.config.ts for dangerous defines
console.log('Checking Vite configuration...');
if (fs.existsSync('./vite.config.ts')) {
  const viteConfigIssues = scanFileForSecrets('./vite.config.ts');
  if (viteConfigIssues.length > 0) {
    console.error('❌ Security issues found in vite.config.ts:');
    viteConfigIssues.forEach(issue => console.error(`  - ${issue}`));
    hasErrors = true;
  }
}

// Check for .env files in repository
console.log('Checking for committed .env files...');
if (fs.existsSync('./.env')) {
  console.error('❌ CRITICAL: .env file exists in repository - this should not be committed!');
  console.error('   Run: git rm --cached .env && echo ".env" >> .gitignore');
  hasErrors = true;
}

// Final result
if (hasErrors) {
  console.error('\n🚨 SECURITY CHECK FAILED!');
  console.error('Fix the above issues before building for production.');
  console.error('\nCommon fixes:');
  console.error('1. Remove VITE_ prefixed secret environment variables');
  console.error('2. Use server-side environment variables for secrets');
  console.error('3. Remove .env file from git repository');
  console.error('4. Update Netlify environment variables to use correct names');
  process.exit(1);
} else {
  console.log('✅ Security check passed!');
  console.log('No secrets found in client-side code.');
}
