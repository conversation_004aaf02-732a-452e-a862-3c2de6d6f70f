-- IMMEDIATE SECURITY FIX for HouseGoing
-- This script removes dangerous SQL execution functions
-- Run this in the Supabase SQL Editor IMMEDIATELY

-- Step 1: Drop all dangerous functions that allow arbitrary SQL execution
DROP FUNCTION IF EXISTS public.exec_sql(text);
DROP FUNCTION IF EXISTS public.pg_query(text);
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.exec_sql_query(text);

-- Drop any other variations
DROP FUNCTION IF EXISTS public.exec_sql;
DROP FUNCTION IF EXISTS public.pg_query;
DROP FUNCTION IF EXISTS public.execute_sql;

-- Step 2: Revoke dangerous permissions
REVOKE ALL ON SCHEMA public FROM anon;
REVOKE ALL ON SCHEMA public FROM authenticated;

-- Step 3: Grant only necessary permissions
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant specific table permissions (adjust as needed)
GRANT SELECT ON public.venues TO anon;
GRANT SELECT ON public.user_profiles TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.user_profiles TO authenticated;
GRANT SELECT ON public.bookings TO authenticated;
GRANT INSERT, UPDATE ON public.bookings TO authenticated;

-- Step 4: Enable Row Level Security on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.venues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;

-- Step 5: Create secure RLS policies
-- User profiles: users can only access their own data
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

-- Bookings: users can only access their own bookings
CREATE POLICY "Users can view own bookings" ON public.bookings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create bookings" ON public.bookings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Venues: public read access, authenticated users can create
CREATE POLICY "Anyone can view venues" ON public.venues
  FOR SELECT USING (true);

CREATE POLICY "Authenticated users can create venues" ON public.venues
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Messages: users can only access their own messages
CREATE POLICY "Users can view own messages" ON public.messages
  FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = recipient_id);

CREATE POLICY "Users can send messages" ON public.messages
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- Reviews: users can view all, but only create their own
CREATE POLICY "Anyone can view reviews" ON public.reviews
  FOR SELECT USING (true);

CREATE POLICY "Users can create own reviews" ON public.reviews
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Success message
SELECT 'CRITICAL SECURITY FIXES APPLIED SUCCESSFULLY!' as result;
SELECT 'Dangerous SQL execution functions removed' as status;
SELECT 'Row Level Security enabled on all tables' as rls_status;
SELECT 'Secure policies created' as policy_status;
