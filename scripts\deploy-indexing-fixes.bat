@echo off
echo 🚀 Deploying indexing fixes to production...

REM Build the production version
echo Building production version...
npm run build:prod

REM Deploy to Netlify
echo Deploying to Netlify...
netlify deploy --prod --dir=dist

echo ✅ Indexing fixes deployed successfully!
echo 📊 Check Google Search Console in 24-48 hours for improvements
echo 📋 Next steps:
echo 1. Submit updated sitemap to Google Search Console
echo 2. Request re-indexing for affected pages
echo 3. Monitor results over the next 7-14 days

pause
