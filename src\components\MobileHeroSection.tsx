import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Search, MapPin, Users, Calendar } from 'lucide-react';
import MobileHorizontalSearch from './MobileHorizontalSearch';

interface MobileHeroSectionProps {
  onSearch: (filters: any) => void;
}

export const MobileHeroSection: React.FC<MobileHeroSectionProps> = ({ onSearch }) => {
  const [searchFilters, setSearchFilters] = useState({
    location: '',
    guests: '',
    date: ''
  });

  const handleSearch = (filters: any) => {
    setSearchFilters(filters);
    onSearch(filters);
  };

  return (
    <div className="bg-gradient-to-br from-orange-600 to-purple-600 text-white">
      <div className="container-width px-3 py-3">
        <div className="text-center mb-2">
          <h1 className="text-base font-bold mb-0.5">
            Find Your Perfect Party Venue
          </h1>
          <p className="text-orange-100 text-xs">
            Book amazing spaces across NSW
          </p>
        </div>

        <MobileHorizontalSearch onSearch={handleSearch} />

        <div className="flex justify-center gap-2 mt-2">
          <Link
            to="/find-venues"
            className="bg-white text-orange-600 px-2.5 py-1 rounded text-xs font-medium hover:bg-gray-100 transition-colors"
          >
            Browse Venues
          </Link>
          <Link
            to="/list-space"
            className="border border-white text-white px-2.5 py-1 rounded text-xs font-medium hover:bg-white hover:text-orange-600 transition-colors"
          >
            List Space
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MobileHeroSection;
