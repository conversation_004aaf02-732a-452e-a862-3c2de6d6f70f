<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - HouseGoing</title>
    <meta name="description" content="HouseGoing privacy policy. Learn how we protect your data and comply with Australian privacy laws.">
    <meta name="keywords" content="privacy policy, data protection, privacy, HouseGoing privacy">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://housegoing.com.au/privacy">
    
    <!-- Open Graph -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://housegoing.com.au/privacy">
    <meta property="og:title" content="Privacy Policy - HouseGoing">
    <meta property="og:description" content="HouseGoing privacy policy. Learn how we protect your data and comply with Australian privacy laws.">
    <meta property="og:image" content="https://housegoing.com.au/og-image.svg">
    <meta property="og:site_name" content="HouseGoing">
    
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://housegoing.com.au/privacy">
    <meta name="twitter:title" content="Privacy Policy - HouseGoing">
    <meta name="twitter:description" content="HouseGoing privacy policy. Learn how we protect your data and comply with Australian privacy laws.">
    <meta name="twitter:image" content="https://housegoing.com.au/og-image.svg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Privacy Policy - HouseGoing",
        "description": "HouseGoing privacy policy. Learn how we protect your data and comply with Australian privacy laws.",
        "url": "https://housegoing.com.au/privacy",
        "publisher": {
            "@type": "Organization",
            "name": "HouseGoing",
            "url": "https://housegoing.com.au"
        }
    }
    </script>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/src/main.tsx" as="script">
    <link rel="preload" href="/src/index.css" as="style">
    
    <!-- Include canonical fix -->
    <script src="/js/canonical-fix.js" defer></script>
</head>
<body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
</body>
</html>