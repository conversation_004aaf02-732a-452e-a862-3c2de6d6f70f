/**
 * Security Configuration
 * Centralized security settings and admin email management
 */

// Environment-based admin emails
export const ADMIN_EMAILS = (() => {
  const adminEmails = import.meta.env.ADMIN_EMAILS || '';
  return adminEmails ? adminEmails.split(',').map(email => email.trim()) : [];
})();

// Notification email configuration
export const NOTIFICATION_EMAILS = {
  HOST: import.meta.env.HOST_NOTIFICATION_EMAIL || ADMIN_EMAILS[0] || '<EMAIL>',
  CUSTOMER: import.meta.env.CUSTOMER_NOTIFICATION_EMAIL || ADMIN_EMAILS[1] || ADMIN_EMAILS[0] || '<EMAIL>',
  GENERAL: ADMIN_EMAILS[0] || '<EMAIL>'
};

// Security validation
export const SECURITY_CONFIG = {
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000, // 15 minutes
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  passwordMinLength: 8,
  requireEmailVerification: true
};

// Rate limiting
export const RATE_LIMITS = {
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // limit each IP to 5 requests per windowMs
  },
  signup: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3 // limit each IP to 3 requests per windowMs
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  }
};

// Helper function to check if email is admin
export function isAdminEmail(email: string): boolean {
  if (!email) return false;
  const normalizedEmail = email.toLowerCase().trim();
  return ADMIN_EMAILS.includes(normalizedEmail);
}

// Helper function to check if email is authorized for specific role
export function isAuthorizedEmail(email: string, role: 'admin' | 'host' | 'customer'): boolean {
  if (!email) return false;
  
  switch (role) {
    case 'admin':
      return isAdminEmail(email);
    case 'host':
      return isAdminEmail(email) || email === NOTIFICATION_EMAILS.HOST;
    case 'customer':
      return true; // All emails can be customers
    default:
      return false;
  }
}

// Environment validation
export function validateEnvironment(): boolean {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'VITE_CLERK_PUBLISHABLE_KEY'
  ];
  
  const missing = required.filter(key => !import.meta.env[key]);
  
  if (missing.length > 0) {
    console.error('Missing required environment variables:', missing);
    return false;
  }
  
  return true;
}

// Security headers configuration
export const SECURITY_HEADERS = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://js.stripe.com https://*.clerk.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.stripe.com https://*.supabase.co https://*.clerk.com;"
};
