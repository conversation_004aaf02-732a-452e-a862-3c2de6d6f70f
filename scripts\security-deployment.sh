#!/bin/bash

# HouseGoing Security Deployment Script
# This script ensures all security measures are in place before deployment

echo "🔒 HouseGoing Security Deployment Check"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track issues
ISSUES=0

# Function to report issues
report_issue() {
    echo -e "${RED}❌ $1${NC}"
    ISSUES=$((ISSUES + 1))
}

report_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

report_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo ""
echo "1. Checking Environment Variables..."
echo "-----------------------------------"

# Check if .env exists
if [ -f ".env" ]; then
    report_issue ".env file exists - this should not be committed to version control"
    echo "   Run: git rm --cached .env && echo '.env' >> .gitignore"
fi

# Check if .env.example exists
if [ ! -f ".env.example" ]; then
    report_issue ".env.example file missing"
else
    report_success ".env.example file exists"
fi

# Check for hardcoded secrets in code
echo ""
echo "2. Scanning for Hardcoded Secrets..."
echo "------------------------------------"

# Check for hardcoded emails
HARDCODED_EMAILS=$(grep -r "<EMAIL>\|<EMAIL>\|<EMAIL>\|<EMAIL>" src/ --exclude-dir=node_modules 2>/dev/null | wc -l)
if [ "$HARDCODED_EMAILS" -gt 0 ]; then
    report_issue "Found $HARDCODED_EMAILS instances of hardcoded personal emails"
    echo "   Run: grep -r '<EMAIL>' src/ to find them"
else
    report_success "No hardcoded personal emails found"
fi

# Check for API keys in code
API_KEYS=$(grep -r "sk_\|pk_live\|pk_test" src/ --exclude-dir=node_modules 2>/dev/null | wc -l)
if [ "$API_KEYS" -gt 0 ]; then
    report_issue "Found $API_KEYS potential API keys in code"
else
    report_success "No hardcoded API keys found"
fi

echo ""
echo "3. Checking Database Security..."
echo "-------------------------------"

if [ -f "supabase/sql/security_fix_immediate.sql" ]; then
    report_success "Database security fix script exists"
    echo "   Make sure to run this in Supabase SQL Editor"
else
    report_warning "Database security fix script not found"
fi

echo ""
echo "4. Checking Security Headers..."
echo "------------------------------"

if [ -f "src/middleware/security.ts" ]; then
    report_success "Security middleware exists"
else
    report_warning "Security middleware not found"
fi

# Check for security headers in build output
if [ -f "dist/_headers" ]; then
    report_success "Security headers file exists for deployment"
else
    report_warning "Security headers file not found in dist/"
fi

echo ""
echo "5. Checking Dependencies..."
echo "--------------------------"

# Check for security vulnerabilities
if command -v npm &> /dev/null; then
    echo "Running npm audit..."
    npm audit --audit-level=high --production 2>/dev/null
    if [ $? -eq 0 ]; then
        report_success "No high-severity vulnerabilities found"
    else
        report_warning "Security vulnerabilities found - run 'npm audit fix'"
    fi
else
    report_warning "npm not found - cannot check for vulnerabilities"
fi

echo ""
echo "6. Final Security Checklist..."
echo "-----------------------------"

echo "Manual checks required:"
echo "□ Regenerated all API keys (Clerk, Supabase, Stripe)"
echo "□ Updated environment variables on hosting platform"
echo "□ Ran database security fix in Supabase"
echo "□ Verified admin email configuration"
echo "□ Tested authentication flows"
echo "□ Enabled HTTPS on production domain"
echo "□ Configured proper CORS origins"

echo ""
echo "======================================="
if [ "$ISSUES" -eq 0 ]; then
    echo -e "${GREEN}🎉 Security check passed! Ready for deployment.${NC}"
    exit 0
else
    echo -e "${RED}🚨 Found $ISSUES security issues that need to be fixed before deployment.${NC}"
    exit 1
fi
