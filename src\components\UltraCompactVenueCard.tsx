import React from 'react';
import { MapPin, Users } from 'lucide-react';
import { Venue } from '../types/venue';

interface UltraCompactVenueCardProps {
  venue: Venue;
  onClick: () => void;
}

export const UltraCompactVenueCard: React.FC<UltraCompactVenueCardProps> = ({ venue, onClick }) => {
  return (
    <div 
      className="bg-white border-b border-gray-100 p-1.5 cursor-pointer hover:bg-gray-50"
      onClick={onClick}
    >
      <div className="flex items-center gap-1.5">
        <img 
          src={venue.images?.[0] || '/placeholder.svg'} 
          alt={venue.title}
          className="w-12 h-12 rounded object-cover flex-shrink-0"
        />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-xs text-gray-900 truncate">{venue.title}</h3>
            <span className="text-orange-600 font-semibold text-xs ml-1">
              ${venue.price}
            </span>
          </div>
          
          <div className="flex items-center gap-2 text-[10px] text-gray-600">
            <span className="flex items-center">
              <MapPin size={8} className="mr-0.5" />
              <span className="truncate">{venue.location}</span>
            </span>
            <span className="flex items-center">
              <Users size={8} className="mr-0.5" />
              {venue.capacity}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UltraCompactVenueCard;
