import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-react';
import { isAdminEmail } from '../config/security';

interface AuthContextType {
  isAuthenticated: boolean;
  user: any | null;
  session: any | null;
  isLoading: boolean;
  error: string | null;
  authProvider: 'clerk';
  signInWithGoogle: () => Promise<void>;
  signInWithEmail: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  customerData: any | null;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded: clerkLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut } = useClerkAuth();
  const [isAdmin, setIsAdmin] = useState(false);

  const isLoading = !clerkLoaded;
  const isAuthenticated = !!isSignedIn && !!clerkUser;
  const authProvider = 'clerk' as const;

  // Use Clerk user data
  const user = clerkUser;
  const session = clerkUser;

  // Check if user is admin using secure environment variables
  useEffect(() => {
    if (clerkUser?.primaryEmailAddress?.emailAddress) {
      const email = clerkUser.primaryEmailAddress.emailAddress;
      setIsAdmin(isAdminEmail(email));
    } else {
      setIsAdmin(false);
    }
  }, [clerkUser]);

  // Clerk auth methods - these will redirect to Clerk's hosted pages
  const signInWithGoogle = async () => {
    try {
      // Redirect to Clerk's Google OAuth flow
      window.location.href = '/sign-in';
    } catch (error) {
      console.error('❌ Error in signInWithGoogle:', error);
    }
  };

  const signInWithEmail = async (email: string) => {
    try {
      // Redirect to Clerk's sign-in page with email pre-filled
      window.location.href = `/sign-in?email=${encodeURIComponent(email)}`;
    } catch (error) {
      console.error('❌ Error in signInWithEmail:', error);
    }
  };

  const signOut = async () => {
    try {
      // Use Clerk's sign out
      await clerkSignOut();

      // Clear customer data
      console.log('✅ Signed out from Clerk');
      window.location.href = '/';
    } catch (error) {
      console.error('❌ Error in signOut:', error);
    }
  };

  const refreshAuth = async () => {
    try {
      console.log('✅ Auth refreshed');
    } catch (error) {
      console.error('❌ Error in refreshAuth:', error);
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    session,
    isLoading,
    error: null,
    authProvider,
    signInWithGoogle,
    signInWithEmail,
    signOut,
    refreshAuth,
    customerData: null,
    isAdmin,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Export the AuthProvider as default for backward compatibility
export default AuthProvider;
