import React from 'react';
import { Link } from 'react-router-dom';
import { Star, Users, MapPin, DollarSign, Clock, Music2 } from 'lucide-react';

interface MobileVenueCardProps {
  id: string;
  image: string;
  title: string;
  price: number;
  location: string;
  capacity: number;
  rating?: number;
  reviews?: number;
  partyScore?: number;
  onQuickBook?: () => void;
}

export const MobileVenueCard: React.FC<MobileVenueCardProps> = ({
  id,
  image,
  title,
  price,
  location,
  capacity,
  rating = 4.5,
  reviews = 12,
  partyScore = 8.5,
  onQuickBook
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-3">
      <div className="flex gap-3">
        {/* Image - Fixed size for mobile */}
        <div className="flex-shrink-0">
          <img
            src={image || "https://images.unsplash.com/photo-1519167758481-83f29c1fe8ea?w=120&h=120&fit=crop"}
            alt={title}
            className="w-24 h-24 rounded-md object-cover"
            loading="lazy"
          />
        </div>

        {/* Content - Horizontal layout */}
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-1">
            <h3 className="font-semibold text-sm text-gray-900 line-clamp-1">
              {title}
            </h3>
            <span className="text-sm font-bold text-orange-600 ml-2">
              ${price}
            </span>
          </div>

          <div className="flex items-center text-xs text-gray-600 mb-1">
            <MapPin className="w-3 h-3 mr-1 flex-shrink-0" />
            <span className="line-clamp-1">{location}</span>
          </div>

          <div className="flex items-center gap-3 text-xs text-gray-600 mb-2">
            <div className="flex items-center">
              <Users className="w-3 h-3 mr-1 flex-shrink-0" />
              <span>{capacity}</span>
            </div>
            <div className="flex items-center">
              <Star className="w-3 h-3 mr-1 text-yellow-500 flex-shrink-0" />
              <span>{rating} ({reviews})</span>
            </div>
            <div className="flex items-center">
              <Music2 className="w-3 h-3 mr-1 text-purple-500 flex-shrink-0" />
              <span>{partyScore}</span>
            </div>
          </div>

          {/* Horizontal action buttons */}
          <div className="flex gap-2">
            <Link
              to={`/venue/${id}`}
              className="flex-1 bg-orange-600 text-white text-xs py-2 px-3 rounded-md text-center font-medium hover:bg-orange-700 transition-colors"
            >
              View Details
            </Link>
            <button
              onClick={onQuickBook}
              className="flex-1 bg-purple-600 text-white text-xs py-2 px-3 rounded-md text-center font-medium hover:bg-purple-700 transition-colors"
            >
              Quick Book
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileVenueCard;
