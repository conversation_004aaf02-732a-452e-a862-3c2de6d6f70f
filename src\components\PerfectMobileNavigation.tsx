import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Home, Search, Heart, User, Plus } from 'lucide-react';

export const PerfectMobileNavigation: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(location.pathname);

  const navItems = [
    { path: '/', icon: Home, label: 'Home' },
    { path: '/find-venues', icon: Search, label: 'Search' },
    { path: '/host', icon: Plus, label: 'List', special: true },
    { path: '/favorites', icon: Heart, label: 'Saved' },
    { path: '/profile', icon: User, label: 'Profile' },
  ];

  const handleNavigation = (path: string) => {
    setActiveTab(path);
    navigate(path);
  };

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 safe-area-bottom">
      <div className="flex justify-around items-center py-2">
        {navItems.map((item) => (
          <button
            key={item.path}
            onClick={() => handleNavigation(item.path)}
            className={`flex flex-col items-center justify-center w-16 h-12 rounded-lg transition-colors ${
              activeTab === item.path
                ? 'text-orange-500'
                : 'text-gray-400 hover:text-gray-600'
            } ${item.special ? 'relative' : ''}`}
          >
            {item.special ? (
              <div className="bg-orange-500 text-white rounded-full p-2 -mt-6 shadow-lg">
                <item.icon size={20} />
              </div>
            ) : (
              <item.icon size={20} />
            )}
            <span className="text-xs mt-1">{item.label}</span>
          </button>
        ))}
      </div>
    </nav>
  );
};
