import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Venue } from '../types/venue';
import MicroSearch from '../components/MicroSearch';
import UltraCompactFeaturedVenues from '../components/UltraCompactFeaturedVenues';
import SEO from '../components/seo/SEO';
import { OrganizationSchema, WebsiteSchema } from '../components/seo/JsonLd';

export default function MobileHomePage() {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAuthSuccess, setShowAuthSuccess] = useState(false);

  // Create proper mock venues
  const venues: Venue[] = [
    {
      id: '1',
      title: 'Sydney CBD Rooftop',
      description: 'Stunning rooftop venue with city views',
      location: 'Sydney CBD',
      price: 150,
      capacity: 50,
      rating: 4.8,
      reviews: 24,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Bathroom'],
      eventTypes: ['Birthday', 'Corporate', 'Wedding'],
      coordinates: { latitude: -33.8688, longitude: 151.2093 },
      host: {
        id: '1',
        name: '<PERSON>',
        image: '/placeholder.svg',
        rating: 4.9
      }
    },
    {
      id: '2',
      title: 'Bondi Beach House',
      description: 'Beachfront property perfect for parties',
      location: 'Bondi Beach',
      price: 200,
      capacity: 30,
      rating: 4.9,
      reviews: 18,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Parking'],
      eventTypes: ['Birthday', 'Beach Party'],
      coordinates: { latitude: -33.8915, longitude: 151.2767 },
      host: {
        id: '2',
        name: 'Mike T',
        image: '/placeholder.svg',
        rating: 4.7
      }
    },
    {
      id: '3',
      title: 'Newtown Warehouse',
      description: 'Industrial space with great acoustics',
      location: 'Newtown',
      price: 120,
      capacity: 80,
      rating: 4.6,
      reviews: 31,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Sound System', 'Bar'],
      eventTypes: ['Birthday', 'Live Music', 'Corporate'],
      coordinates: { latitude: -33.8969, longitude: 151.1831 },
      host: {
        id: '3',
        name: 'Alex R',
        image: '/placeholder.svg',
        rating: 4.8
      }
    },
    {
      id: '4',
      title: 'Manly Penthouse',
      description: 'Luxury penthouse with harbor views',
      location: 'Manly',
      price: 300,
      capacity: 25,
      rating: 5.0,
      reviews: 12,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Balcony', 'Parking'],
      eventTypes: ['Birthday', 'Anniversary', 'Corporate'],
      coordinates: { latitude: -33.7979, longitude: 151.2845 },
      host: {
        id: '4',
        name: 'Emma L',
        image: '/placeholder.svg',
        rating: 5.0
      }
    },
    {
      id: '5',
      title: 'Surry Hills Studio',
      description: 'Modern studio in trendy area',
      location: 'Surry Hills',
      price: 100,
      capacity: 40,
      rating: 4.7,
      reviews: 28,
      images: ['/placeholder.svg'],
      amenities: ['WiFi', 'Kitchen', 'Projector'],
      eventTypes: ['Birthday', 'Workshop', 'Meeting'],
      coordinates: { latitude: -33.8831, longitude: 151.2169 },
      host: {
        id: '5',
        name: 'James K',
        image: '/placeholder.svg',
        rating: 4.6
      }
    }
  ];

  // Check if we're coming from a successful authentication
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const authStatus = params.get('auth');

    if (authStatus === 'success') {
      setShowAuthSuccess(true);

      // Hide the success message after 3 seconds
      const timer = setTimeout(() => {
        setShowAuthSuccess(false);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [location]);

  const handleSearch = (filters: any) => {
    navigate('/find-venues', { state: { filters } });
  };

  return (
    <>
      <SEO
        title="Find Your Perfect Party Venue | HouseGoing"
        description="Discover party-ready venues across NSW. Find the perfect space for your next celebration."
        url="https://housegoing.com.au/"
      />
      <OrganizationSchema />
      <WebsiteSchema />

      {/* Success message - compact */}
      {showAuthSuccess && (
        <div className="bg-green-100 text-green-700 px-2 py-1 text-xs text-center">
          Welcome back!
        </div>
      )}

      {/* Ultra-compact search */}
      <MicroSearch onSearch={handleSearch} />

      {/* Ultra-compact featured venues */}
      <UltraCompactFeaturedVenues venues={venues} />

      {/* Quick actions */}
      <div className="bg-white border-t border-gray-100 p-2">
        <div className="grid grid-cols-2 gap-2">
          <button 
            onClick={() => navigate('/find-venues')}
            className="bg-orange-500 text-white text-xs py-2 rounded"
          >
            Browse All
          </button>
          <button 
            onClick={() => navigate('/host')}
            className="border border-orange-500 text-orange-500 text-xs py-2 rounded"
          >
            List Space
          </button>
        </div>
      </div>
    </>
  );
}
