# 🚨 CRITICAL: Netlify Deployment Security Fix

## **THE PROBLEM**
Netlify is detecting `SUPABASE_SERVICE_ROLE_KEY` in your build output because you have `VITE_SUPABASE_SERVICE_ROLE_KEY` in your environment variables. **VITE_ prefixed variables are exposed to client-side code**, which is a critical security vulnerability.

## **IMMEDIATE FIXES REQUIRED**

### **1. Update Netlify Environment Variables**

Go to your Netlify dashboard → Site settings → Environment variables and make these changes:

#### **❌ REMOVE THESE (Client-exposed secrets):**
```
VITE_SUPABASE_SERVICE_ROLE_KEY  ← DELETE THIS IMMEDIATELY
```

#### **✅ KEEP THESE (Server-side only):**
```
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here
CLOUDINARY_API_SECRET=your_cloudinary_secret_here
```

#### **✅ KEEP THESE (Client-safe public keys):**
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

### **2. Add Missing Environment Variables**

Add these new secure environment variables to Netlify:

```
# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
HOST_NOTIFICATION_EMAIL=<EMAIL>
CUSTOMER_NOTIFICATION_EMAIL=<EMAIL>

# Email Service
BREVO_SMTP_HOST=smtp-relay.brevo.com
BREVO_SMTP_PORT=587
BREVO_SMTP_USER=your_brevo_user
BREVO_SMTP_PASS=your_brevo_password
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_app_password

# Backend
VITE_BACKEND_URL=https://housegoing.onrender.com
```

### **3. Verify Environment Variable Names**

Make sure your Netlify environment variables match exactly:

| Variable Name | Type | Used For |
|---------------|------|----------|
| `SUPABASE_SERVICE_ROLE_KEY` | Server-only | Netlify functions |
| `VITE_SUPABASE_ANON_KEY` | Client-safe | Frontend Supabase client |
| `CLERK_SECRET_KEY` | Server-only | Clerk webhooks |
| `VITE_CLERK_PUBLISHABLE_KEY` | Client-safe | Frontend Clerk auth |

## **DEPLOYMENT STEPS**

### **Step 1: Update Environment Variables**
1. Go to Netlify Dashboard
2. Site Settings → Environment Variables
3. **DELETE** `VITE_SUPABASE_SERVICE_ROLE_KEY`
4. **ADD** all the variables listed above

### **Step 2: Clear Build Cache**
1. In Netlify Dashboard: Site Settings → Build & Deploy
2. Click "Clear cache and deploy site"

### **Step 3: Deploy**
```bash
git add .
git commit -m "fix: remove client-exposed secrets from build"
git push origin main
```

## **SECURITY VERIFICATION**

After deployment, verify security:

1. **Check build logs** - should show no secret detection errors
2. **Inspect client bundle** - run in browser console:
   ```javascript
   // This should be undefined (good)
   console.log(import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY);
   
   // These should work (expected)
   console.log(import.meta.env.VITE_SUPABASE_ANON_KEY);
   console.log(import.meta.env.VITE_CLERK_PUBLISHABLE_KEY);
   ```

3. **Test functionality** - ensure auth and database operations still work

## **WHY THIS HAPPENED**

- **VITE_ prefixed** environment variables are automatically exposed to client-side code
- **Service role keys** should NEVER be in client-side code
- **Netlify's secret scanner** correctly detected this security vulnerability

## **PREVENTION**

- ✅ Use `VITE_` prefix only for public/publishable keys
- ✅ Use regular names (no `VITE_` prefix) for secrets
- ✅ Run security check before each deployment: `npm run security:check`

## **EMERGENCY CONTACTS**

If you need immediate help:
- **Netlify Support:** https://www.netlify.com/support/
- **Supabase Support:** https://supabase.com/support
- **Clerk Support:** https://clerk.com/support

---

## **QUICK CHECKLIST**

- [ ] Removed `VITE_SUPABASE_SERVICE_ROLE_KEY` from Netlify
- [ ] Added all required server-side environment variables
- [ ] Cleared Netlify build cache
- [ ] Deployed and verified no secret detection errors
- [ ] Tested that authentication still works
- [ ] Verified admin functions work correctly

**🎉 Once complete, your deployment should succeed without security errors!**
