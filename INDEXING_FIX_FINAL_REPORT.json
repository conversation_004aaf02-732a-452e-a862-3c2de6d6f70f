{"timestamp": "2025-07-28T13:48:05.750Z", "issues": {"alternativePageWithCanonical": {"count": 50, "description": "Pages with spa=true parameter causing canonical issues", "fix": "301 redirects to clean URLs without parameters"}, "soft404": {"count": 32, "description": "Soft 404 errors likely from missing content or incorrect redirects", "fix": "Ensure all venue pages exist and return proper 200 status"}, "pageWithRedirect": {"count": 11, "description": "Pages that redirect instead of serving content directly", "fix": "Minimize redirect chains and ensure direct access to content"}, "excludedByNoindex": {"count": 4, "description": "Pages blocked by noindex meta tag", "fix": "Review and remove noindex tags where appropriate"}, "crawledNotIndexed": {"count": 22, "description": "Pages crawled but not indexed by Google", "fix": "Improve content quality and ensure unique, valuable content"}, "duplicateWithoutCanonical": {"count": 1, "description": "Duplicate pages without proper canonical tags", "fix": "Add canonical tags to preferred versions"}, "blockedByRobots": {"count": 2, "description": "Pages blocked by robots.txt", "fix": "Review robots.txt rules and allow important pages"}}, "fixes": {"redirects": "Updated netlify.toml with parameter cleanup redirects", "sitemap": "Regenerated sitemap.xml with clean URLs", "canonical": "Added canonical tags to all pages", "robots": "Updated robots.txt to allow all important pages", "meta": "Removed noindex tags from public pages"}}