/**
 * Admin Component for Customer Verification Approval
 * Allows admins to review and approve/reject customer identity verification
 */

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Eye, 
  FileText, 
  Camera, 
  User, 
  Calendar,
  Mail,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { supabase } from '../../lib/supabase';
import toast from 'react-hot-toast';

interface PendingVerification {
  user_id: string;
  email: string;
  full_name?: string;
  created_at: string;
  documents: {
    id_front?: {
      cloudinary_secure_url: string;
      upload_timestamp: string;
    };
    id_back?: {
      cloudinary_secure_url: string;
      upload_timestamp: string;
    };
    selfie_thumbs_up?: {
      cloudinary_secure_url: string;
      upload_timestamp: string;
    };
  };
  verification_status: string;
}

export default function CustomerVerificationApproval() {
  const { user } = useUser();
  const [pendingVerifications, setPendingVerifications] = useState<PendingVerification[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingUserId, setProcessingUserId] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<PendingVerification | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    fetchPendingVerifications();
  }, []);

  const fetchPendingVerifications = async () => {
    try {
      setLoading(true);
      
      // Get all users with pending verification documents
      const { data: documents, error: docsError } = await supabase
        .from('user_verification_documents')
        .select(`
          user_id,
          document_type,
          cloudinary_secure_url,
          upload_timestamp,
          verification_status
        `)
        .eq('verification_status', 'pending');

      if (docsError) {
        console.error('Error fetching documents:', docsError);
        toast.error('Failed to load pending verifications');
        return;
      }

      // Get user profiles for the users with pending documents
      const userIds = [...new Set(documents?.map(doc => doc.user_id) || [])];
      
      if (userIds.length === 0) {
        setPendingVerifications([]);
        return;
      }

      const { data: profiles, error: profilesError } = await supabase
        .from('user_profiles')
        .select('user_id, email, full_name, created_at')
        .in('user_id', userIds);

      if (profilesError) {
        console.error('Error fetching profiles:', profilesError);
        toast.error('Failed to load user profiles');
        return;
      }

      // Group documents by user and create verification objects
      const verifications: PendingVerification[] = profiles?.map(profile => {
        const userDocs = documents?.filter(doc => doc.user_id === profile.user_id) || [];
        const documentsObj: any = {};
        
        userDocs.forEach(doc => {
          documentsObj[doc.document_type] = {
            cloudinary_secure_url: doc.cloudinary_secure_url,
            upload_timestamp: doc.upload_timestamp
          };
        });

        return {
          user_id: profile.user_id,
          email: profile.email,
          full_name: profile.full_name,
          created_at: profile.created_at,
          documents: documentsObj,
          verification_status: 'pending'
        };
      }) || [];

      // Only include users who have uploaded all 3 required documents
      const completeVerifications = verifications.filter(verification => 
        verification.documents.id_front && 
        verification.documents.id_back && 
        verification.documents.selfie_thumbs_up
      );

      setPendingVerifications(completeVerifications);
    } catch (error) {
      console.error('Error fetching pending verifications:', error);
      toast.error('Failed to load pending verifications');
    } finally {
      setLoading(false);
    }
  };

  const handleApproveVerification = async (userId: string) => {
    if (!user?.id) {
      toast.error('Admin authentication required');
      return;
    }

    setProcessingUserId(userId);
    
    try {
      const { data, error } = await supabase
        .rpc('admin_approve_customer_verification', {
          p_user_id: userId,
          p_admin_id: user.id,
          p_verification_level: 'enhanced'
        });

      if (error) {
        console.error('Error approving verification:', error);
        toast.error('Failed to approve verification');
        return;
      }

      if (data && data.length > 0 && data[0].success) {
        toast.success(`Verification approved for ${data[0].user_email}`);
        // Remove from pending list
        setPendingVerifications(prev => 
          prev.filter(v => v.user_id !== userId)
        );
      } else {
        toast.error(data?.[0]?.message || 'Failed to approve verification');
      }
    } catch (error) {
      console.error('Error approving verification:', error);
      toast.error('Failed to approve verification');
    } finally {
      setProcessingUserId(null);
    }
  };

  const handleRejectVerification = async (userId: string, reason: string) => {
    if (!user?.id) {
      toast.error('Admin authentication required');
      return;
    }

    if (!reason.trim()) {
      toast.error('Please provide a rejection reason');
      return;
    }

    setProcessingUserId(userId);
    
    try {
      const { data, error } = await supabase
        .rpc('admin_reject_customer_verification', {
          p_user_id: userId,
          p_admin_id: user.id,
          p_rejection_reason: reason
        });

      if (error) {
        console.error('Error rejecting verification:', error);
        toast.error('Failed to reject verification');
        return;
      }

      if (data && data.length > 0 && data[0].success) {
        toast.success(`Verification rejected for ${data[0].user_email}`);
        // Remove from pending list
        setPendingVerifications(prev => 
          prev.filter(v => v.user_id !== userId)
        );
        setSelectedUser(null);
        setRejectionReason('');
      } else {
        toast.error(data?.[0]?.message || 'Failed to reject verification');
      }
    } catch (error) {
      console.error('Error rejecting verification:', error);
      toast.error('Failed to reject verification');
    } finally {
      setProcessingUserId(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        <span className="ml-3 text-gray-600">Loading pending verifications...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Customer Verification Approval</h2>
          <p className="text-gray-600 mt-1">
            Review and approve customer identity verification for $1 booking eligibility
          </p>
        </div>
        <div className="bg-blue-50 px-4 py-2 rounded-lg">
          <span className="text-blue-800 font-semibold">{pendingVerifications.length}</span>
          <span className="text-blue-600 text-sm ml-1">pending</span>
        </div>
      </div>

      {pendingVerifications.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
          <p className="text-gray-600">No pending customer verifications to review.</p>
        </div>
      ) : (
        <div className="grid gap-6">
          {pendingVerifications.map((verification) => (
            <div key={verification.user_id} className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="bg-orange-100 rounded-full p-2 mr-3">
                    <Shield className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{verification.full_name || 'Name not provided'}</h3>
                    <div className="flex items-center text-sm text-gray-600 mt-1">
                      <Mail className="h-4 w-4 mr-1" />
                      {verification.email}
                    </div>
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                      <Calendar className="h-4 w-4 mr-1" />
                      Submitted {new Date(verification.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                    Pending Review
                  </span>
                </div>
              </div>

              {/* Document Preview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {['id_front', 'id_back', 'selfie_thumbs_up'].map((docType) => {
                  const doc = verification.documents[docType as keyof typeof verification.documents];
                  const labels = {
                    id_front: 'ID Front',
                    id_back: 'ID Back', 
                    selfie_thumbs_up: 'Selfie with Thumbs Up'
                  };
                  
                  return (
                    <div key={docType} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          {labels[docType as keyof typeof labels]}
                        </span>
                        {doc ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      {doc ? (
                        <div className="space-y-2">
                          <img 
                            src={doc.cloudinary_secure_url} 
                            alt={labels[docType as keyof typeof labels]}
                            className="w-full h-32 object-cover rounded border cursor-pointer hover:opacity-80"
                            onClick={() => window.open(doc.cloudinary_secure_url, '_blank')}
                          />
                          <p className="text-xs text-gray-500">
                            Uploaded {new Date(doc.upload_timestamp).toLocaleDateString()}
                          </p>
                        </div>
                      ) : (
                        <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-gray-400 text-sm">Not uploaded</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <button
                  onClick={() => setSelectedUser(verification)}
                  className="flex items-center px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  disabled={processingUserId === verification.user_id}
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </button>
                
                <button
                  onClick={() => handleApproveVerification(verification.user_id)}
                  disabled={processingUserId === verification.user_id}
                  className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {processingUserId === verification.user_id ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve Verification
                    </>
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Rejection Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Reject Verification</h3>
            </div>
            
            <p className="text-gray-600 mb-4">
              Please provide a reason for rejecting {selectedUser.email}'s verification:
            </p>
            
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="e.g., Document image is unclear, ID appears to be expired, etc."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24 mb-4"
            />
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setSelectedUser(null);
                  setRejectionReason('');
                }}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleRejectVerification(selectedUser.user_id, rejectionReason)}
                disabled={!rejectionReason.trim() || processingUserId === selectedUser.user_id}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {processingUserId === selectedUser.user_id ? 'Processing...' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
