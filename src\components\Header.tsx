import React, { useState, useEffect } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { Menu, Home, Search, Calendar, User } from 'lucide-react';
import Logo from './navigation/Logo';
import MainNav from './navigation/MainNav';
import MobileMenu from './navigation/MobileMenu';
import HeaderAuthButtons from './auth/HeaderAuthButtons';
import HeaderMobileAuthStatus from './auth/HeaderMobileAuthStatus';

interface HeaderProps {
  isMobile?: boolean;
}

export default function Header({ isMobile = false }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const location = useLocation();

  const controlHeader = () => {
    if (typeof window !== 'undefined') {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    }
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', controlHeader);
      return () => {
        window.removeEventListener('scroll', controlHeader);
      };
    }
  }, [lastScrollY]);

  React.useEffect(() => {
    console.log('Header component rendered - Should include MainNav with NSW Party Planning link');
    console.log('Current location:', location.pathname);
  }, [location.pathname]);

  return (
    <>
      {/* Mobile Bottom Navigation */}
      {isMobile && (
        <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 safe-area-bottom">
          <div className="flex justify-around items-center h-16">
            <Link 
              to="/" 
              className={`flex flex-col items-center justify-center w-full h-full touch-target transition-colors ${
                location.pathname === '/' ? 'text-orange-600' : 'text-gray-600 hover:text-orange-600'
              }`}
            >
              <Home className="h-6 w-6" />
              <span className="text-xs mt-1 font-medium">Home</span>
            </Link>
            <Link 
              to="/find-venues" 
              className={`flex flex-col items-center justify-center w-full h-full touch-target transition-colors ${
                location.pathname === '/find-venues' ? 'text-orange-600' : 'text-gray-600 hover:text-orange-600'
              }`}
            >
              <Search className="h-6 w-6" />
              <span className="text-xs mt-1 font-medium">Search</span>
            </Link>
            <Link 
              to="/my-bookings" 
              className={`flex flex-col items-center justify-center w-full h-full touch-target transition-colors ${
                location.pathname === '/my-bookings' ? 'text-orange-600' : 'text-gray-600 hover:text-orange-600'
              }`}
            >
              <Calendar className="h-6 w-6" />
              <span className="text-xs mt-1 font-medium">Bookings</span>
            </Link>
            <Link 
              to="/my-account" 
              className={`flex flex-col items-center justify-center w-full h-full touch-target transition-colors ${
                location.pathname === '/my-account' ? 'text-orange-600' : 'text-gray-600 hover:text-orange-600'
              }`}
            >
              <User className="h-6 w-6" />
              <span className="text-xs mt-1 font-medium">Account</span>
            </Link>
          </div>
        </nav>
      )}

      {/* Main Header - Now with scroll hide behavior */}
      <header className={`fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md border-b border-gray-100 transition-transform duration-300 z-50 ${
        isVisible ? 'translate-y-0' : '-translate-y-full'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center py-4">
            {/* Left side - Logo */}
            <Logo />

            {/* Center - Main Navigation (Desktop only) */}
            <div className="hidden md:flex flex-grow justify-center">
              <MainNav />
            </div>

            {/* Right side - Auth buttons and mobile menu */}
            <div className="flex items-center space-x-4">
              {/* Desktop auth buttons */}
              {!isMobile && (
                <div className="hidden md:block">
                  <HeaderAuthButtons />
                </div>
              )}

              {/* Mobile menu button - Enhanced for touch */}
              {isMobile && (
                <button
                  className="md:hidden btn-reactive p-3 rounded-xl hover:bg-gray-100 active:bg-gray-200 touch-target focus-enhanced min-h-[52px] min-w-[52px] flex items-center justify-center transition-all duration-200"
                  aria-label="Open menu"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  <Menu className="h-7 w-7 text-gray-700" />
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        currentPath={location.pathname}
      />
    </>
  );
}
