import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Venue } from '../types/venue';
import UltraCompactVenueCard from './UltraCompactVenueCard';

interface UltraCompactFeaturedVenuesProps {
  venues: Venue[];
}

export const UltraCompactFeaturedVenues: React.FC<UltraCompactFeaturedVenuesProps> = ({ venues }) => {
  const navigate = useNavigate();

  const handleVenueClick = (venue: Venue) => {
    navigate(`/venue/${venue.id}`);
  };

  return (
    <div className="bg-white">
      <div className="px-2 py-1 border-b border-gray-100">
        <h2 className="text-sm font-semibold text-gray-900">Top Venues</h2>
      </div>
      <div className="divide-y divide-gray-100">
        {venues.slice(0, 5).map((venue) => (
          <UltraCompactVenueCard
            key={venue.id}
            venue={venue}
            onClick={() => handleVenueClick(venue)}
          />
        ))}
      </div>
    </div>
  );
};

export default UltraCompactFeaturedVenues;
