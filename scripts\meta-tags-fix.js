
// Add to your React app's main component or index.html
// This ensures proper canonical tags and removes noindex from public pages

// Canonical tag component
function CanonicalTag() {
  const location = useLocation();
  
  useEffect(() => {
    // Remove existing canonical tag
    const existingCanonical = document.querySelector('link[rel="canonical"]');
    if (existingCanonical) {
      existingCanonical.remove();
    }
    
    // Add new canonical tag
    const canonical = document.createElement('link');
    canonical.rel = 'canonical';
    canonical.href = window.location.origin + location.pathname;
    document.head.appendChild(canonical);
    
    // Ensure noindex is removed from public pages
    const metaRobots = document.querySelector('meta[name="robots"]');
    if (metaRobots && metaRobots.content.includes('noindex')) {
      metaRobots.content = 'index,follow';
    }
  }, [location.pathname]);
  
  return null;
}

// Usage: Add <CanonicalTag /> to your main App component
