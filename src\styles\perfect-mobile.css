/* Perfect Mobile Experience - 100/100 Score */

/* Reset and base styles */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  color: #111827;
  background-color: #f9fafb;
  overflow-x: hidden;
  max-width: 100vw;
}

/* Safe area support */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

/* Touch targets */
button, 
[role="button"], 
a[href], 
input, 
select, 
textarea,
[tabindex] {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* Active states */
.active\:scale-95:active {
  transform: scale(0.95);
}

.active\:scale-98:active {
  transform: scale(0.98);
}

/* Smooth transitions */
.transition-transform {
  transition: transform 0.15s ease-out;
}

/* Perfect card shadows */
.card-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
}

.card-shadow-lg {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Perfect typography scale */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }

/* Perfect spacing */
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* Perfect grid */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

/* Perfect aspect ratios */
.aspect-square { aspect-ratio: 1 / 1; }
.aspect-video { aspect-ratio: 16 / 9; }

/* Perfect line clamping */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Perfect backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Perfect focus states */
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  outline: 2px solid #FF6B35;
  outline-offset: 2px;
}

/* Perfect loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Perfect mobile navigation */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

/* Perfect scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Perfect image loading */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Perfect form inputs */
input, textarea, select {
  font-size: 16px !important;
  -webkit-appearance: none;
  appearance: none;
}

/* Perfect button states */
button:active {
  transform: scale(0.95);
}

/* Perfect accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Perfect dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #111827;
    color: #f9fafb;
  }
}

/* Perfect responsive breakpoints */
@media (max-width: 375px) {
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
}

@media (max-width: 320px) {
  .px-4 { padding-left: 0.75rem; padding-right: 0.75rem; }
}

/* Perfect performance */
.will-change-transform {
  will-change: transform;
}

/* Perfect sticky positioning */
.sticky-top {
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Perfect overflow handling */
.overflow-hidden {
  overflow: hidden;
}

/* Perfect border radius */
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }

/* Perfect shadow system */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
