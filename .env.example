# HouseGoing Environment Variables
# Copy this to .env and fill with your actual values
# NEVER commit .env to version control

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Clerk Configuration
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key_here
CLERK_SECRET_KEY=your_clerk_secret_key_here
VITE_CLERK_DOMAIN=your_clerk_domain_here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# Admin Configuration
ADMIN_EMAILS=<EMAIL>,<EMAIL>
HOST_NOTIFICATION_EMAIL=<EMAIL>
CUSTOMER_NOTIFICATION_EMAIL=<EMAIL>

# Backend Configuration
VITE_BACKEND_URL=https://your-backend-url.com

# Environment
NODE_ENV=production
