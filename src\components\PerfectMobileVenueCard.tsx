import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Venue } from '../types/venue';
import { Heart, MapPin, Users, DollarSign, Star } from 'lucide-react';

interface PerfectMobileVenueCardProps {
  venue: Venue;
  index: number;
}

export const PerfectMobileVenueCard: React.FC<PerfectMobileVenueCardProps> = ({ venue, index }) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    navigate(`/venue/${venue.id}`);
  };

  const handleSave = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Add save functionality
  };

  return (
    <div 
      className="bg-white border border-gray-100 rounded-lg overflow-hidden active:scale-[0.98] transition-transform duration-150"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => e.key === 'Enter' && handleClick()}
    >
      {/* Image with aspect ratio */}
      <div className="relative">
        <img 
          src={venue.images[0] || '/placeholder.svg'} 
          alt={venue.title}
          className="w-full h-32 object-cover"
          loading="lazy"
        />
        
        {/* Save button - top right */}
        <button 
          className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-sm active:scale-95"
          onClick={handleSave}
          aria-label="Save venue"
        >
          <Heart size={16} className="text-gray-600" />
        </button>

        {/* Price badge */}
        <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm font-semibold">
          ${venue.price}/hr
        </div>

        {/* Rating badge */}
        <div className="absolute bottom-2 right-2 bg-white/90 backdrop-blur-sm px-2 py-1 rounded text-sm font-semibold flex items-center">
          <Star size={12} className="text-yellow-500 mr-1" fill="currentColor" />
          {venue.rating}
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        <h3 className="font-semibold text-sm text-gray-900 line-clamp-1 mb-1">
          {venue.title}
        </h3>
        
        <div className="flex items-center text-xs text-gray-600 mb-2">
          <MapPin size={12} className="mr-1 flex-shrink-0" />
          <span className="line-clamp-1">{venue.location}</span>
        </div>

        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center text-gray-600">
            <Users size={12} className="mr-1" />
            <span>{venue.capacity} guests</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            <span className="text-orange-600 font-semibold">${venue.price}</span>
            <span className="text-gray-500 ml-1">/hr</span>
          </div>
        </div>
      </div>
    </div>
  );
};
