import React, { useState } from 'react';
import { Search, MapPin, Users, Calendar, Loader } from 'lucide-react';

interface MobileHorizontalSearchProps {
  onSearch: (filters: any) => void;
}

export const MobileHorizontalSearch: React.FC<MobileHorizontalSearchProps> = ({ onSearch }) => {
  const [location, setLocation] = useState('');
  const [guests, setGuests] = useState('');
  const [date, setDate] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    setLoading(true);
    await onSearch({ location, guests, date });
    setLoading(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-2">
      <div className="flex items-center gap-1">
        <div className="flex-1 min-w-0">
          <div className="relative">
            <MapPin className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
            <input
              type="text"
              placeholder="Location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="w-full pl-6 pr-2 py-1.5 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-orange-500"
            />
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="relative">
            <Users className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
            <select
              value={guests}
              onChange={(e) => setGuests(e.target.value)}
              className="w-full pl-6 pr-2 py-1.5 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-orange-500"
            >
              <option value="">Guests</option>
              <option value="1-10">1-10</option>
              <option value="11-25">11-25</option>
              <option value="26-50">26-50</option>
              <option value="51-100">51-100</option>
              <option value="100+">100+</option>
            </select>
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="relative">
            <Calendar className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="w-full pl-6 pr-2 py-1.5 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-orange-500"
            />
          </div>
        </div>

        <button
          onClick={handleSearch}
          disabled={loading}
          className="bg-orange-600 text-white p-1.5 rounded hover:bg-orange-700 transition-colors disabled:opacity-50"
        >
          {loading ? (
            <Loader className="animate-spin w-3 h-3" />
          ) : (
            <Search className="w-3 h-3" />
          )}
        </button>
      </div>
    </div>
  );
};

export default MobileHorizontalSearch;
